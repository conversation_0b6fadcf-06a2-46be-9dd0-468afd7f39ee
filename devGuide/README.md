# Developer Guide Directory

This directory contains all development-related documentation for Stirling PDF.

## 📚 Documentation Index

### Core Development
- **[DeveloperGuide.md](./DeveloperGuide.md)** - Main developer setup and architecture guide
- **[EXCEPTION_HANDLING_GUIDE.md](./EXCEPTION_HANDLING_GUIDE.md)** - Exception handling patterns and i18n best practices
- **[HowToAddNewLanguage.md](./HowToAddNewLanguage.md)** - Internationalization and translation guide

### Features & Documentation
- **[AGENTS.md](./AGENTS.md)** - Agent-based functionality documentation
- **[USERS.md](./USERS.md)** - User-focused documentation and guides

## 🔗 Related Files in Root
- **[README.md](../README.md)** - Project overview and quick start
- **[CONTRIBUTING.md](../CONTRIBUTING.md)** - Contribution guidelines
- **[SECURITY.md](../SECURITY.md)** - Security policies and reporting
- **[DATABASE.md](../DATABASE.md)** - Database setup and configuration (usage guide)
- **[HowToUseOCR.md](../HowToUseOCR.md)** - OCR setup and configuration (usage guide)

## 📝 Contributing to Documentation

When adding new development documentation:
1. Place technical guides in this `devGuide/` directory
2. Update this index file with a brief description
3. Keep user-facing docs (README, CONTRIBUTING, SECURITY) in the root
4. Follow existing naming conventions (PascalCase for guides)