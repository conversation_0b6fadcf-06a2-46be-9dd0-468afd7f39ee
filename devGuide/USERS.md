# Who is using Stirling-PDF?

Understanding the diverse applications of Stirling-PDF can be an invaluable resource for collaboration and learning. This page provides a directory of users and use cases. If you are using Stirling-PDF, consider sharing your experiences to help others and foster a community of best practices.

## Adding Yourself as a User

If you're using Stirling-PDF or have integrated it into your platform or workflow, please consider contributing to this list by describing your use case. You can do this by opening a pull request to this file and adding your details in the format below:

- **N**: Name of the organization or individual.
- **D**: A brief description of your usage.
- **U**: Specific features or capabilities utilized.
- **L**: Optional link for further information (e.g., website, blog post).
- **Q**: Contact information for sharing insights (optional).

Example entry:

```
* N: Example Corp
  D: Using Stirling-PDF for automated document processing in our SaaS platform focusing on compression.
  U: OCR, merging PDFs, metadata editing, encryption, compression.
  L: https://example.com/stirling-pdf
  Q: @example-user on Discord/email
```

---

## Requirements for Listing

- You must represent the entity you're listing and ensure the details are accurate.
- Trial deployments are welcome if they represent a realistic evaluation of Stirling-PDF in action.
- Community contributions, including home-lab setups or non-commercial uses, are encouraged.

---

## Users (Alphabetically)

    * N: 
      D: 
      U: 
      L: 

    * N: 
      D: 
      U: 
      L: 
