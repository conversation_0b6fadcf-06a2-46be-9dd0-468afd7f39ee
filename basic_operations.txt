    @PostMapping("/merge-pdfs")
    @Operation(
            summary = "微信小程序合并PDF",
            description =
                    "专为微信小程序设计的PDF合并接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持多文件合并，可自定义排序方式和合并选项。")
    public ResponseEntity<WechatFileResponse> mergePdfs(@RequestBody WechatMergeRequest request) {
        log.info(
                "收到微信小程序PDF合并请求: 文件数量={}, 输出文件名={}",
                request.getFileDataList() != null ? request.getFileDataList().size() : 0,
                request.getOutputFileName());

        try {
            WechatFileResponse response = wechatFileService.processMergePdfs(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF合并处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF合并处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF合并处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/split-pdf")
    @Operation(
            summary = "微信小程序PDF分割",
            description =
                    "专为微信小程序设计的PDF分割接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持按页面范围分割PDF文档。")
    public ResponseEntity<WechatFileResponse> splitPdf(@RequestBody WechatSplitRequest request) {
        log.info("收到微信小程序PDF分割请求: 文件={}, 页面范围={}", request.getFileName(), request.getPageNumbers());

        try {
            WechatFileResponse response = wechatFileService.processSplitPdf(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF分割处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF分割处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF分割处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/compress-pdf")
    @Operation(
            summary = "微信小程序PDF压缩",
            description =
                    "专为微信小程序设计的PDF压缩接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持多级压缩选项，可自定义图片质量和优化级别。")
    public ResponseEntity<WechatFileResponse> compressPdf(@RequestBody WechatCompressRequest request) {
        log.info(
                "收到微信小程序PDF压缩请求: 文件={}, 优化级别={}",
                request.getFileName(),
                request.getOptimizeLevel());

        try {
            WechatFileResponse response = wechatFileService.processCompressPdf(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF压缩处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF压缩处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF压缩处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/rotate-pdf")
    @Operation(
            summary = "微信小程序PDF旋转",
            description =
                    "专为微信小程序设计的PDF旋转接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持90度、180度、270度旋转，可指定页面范围。")
    public ResponseEntity<WechatFileResponse> rotatePdf(@RequestBody WechatRotateRequest request) {
        log.info(
                "收到微信小程序PDF旋转请求: 文件={}, 角度={}",
                request.getFileName(),
                request.getAngle());

        try {
            WechatFileResponse response = wechatFileService.processRotatePdf(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF旋转处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF旋转处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF旋转处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/remove-pages")
    @Operation(
            summary = "微信小程序PDF删除页面",
            description =
                    "专为微信小程序设计的PDF删除页面接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持删除指定页面范围。")
    public ResponseEntity<WechatFileResponse> removePages(@RequestBody WechatRemovePagesRequest request) {
        log.info("收到微信小程序PDF删除页面请求: 文件={}, 删除页面={}", request.getFileName(), request.getPagesToDelete());

        try {
            WechatFileResponse response = wechatFileService.processRemovePages(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF删除页面处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF删除页面处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF删除页面处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/rearrange-pages")
    @Operation(
            summary = "微信小程序PDF重排页面",
            description =
                    "专为微信小程序设计的PDF重排页面接口。"
                            + "使用JSON格式传输文件数据（base64编码），避免二进制传输问题。"
                            + "支持自定义页面顺序重新排列。")
    public ResponseEntity<WechatFileResponse> rearrangePages(@RequestBody WechatRearrangeRequest request) {
        log.info("收到微信小程序PDF重排页面请求: 文件={}, 页面顺序={}", request.getFileName(), request.getPageOrder());

        try {
            WechatFileResponse response = wechatFileService.processRearrangePages(request);

            if (response.isSuccess()) {
                log.info(
                        "PDF重排页面处理成功: 文件={}, 大小={}bytes, 耗时={}ms",
                        response.getFileName(),
                        response.getFileSize(),
                        response.getProcessingTime());
            } else {
                log.warn("PDF重排页面处理失败: {}", response.getMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("PDF重排页面处理异常: {}", e.getMessage(), e);
            WechatFileResponse errorResponse = WechatFileResponse.error("系统内部错误", e);
            return ResponseEntity.ok(errorResponse);
        }
    }
