// pages/merge/merge.js
const { PDFService, AnalysisService } = require('../../utils/api.js');
const { errorHandler } = require('../../utils/error-handler.js');

Page({
  data: {
    // 文件列表
    fileList: [],

    // 输出设置
    outputFileName: '合并文件',

    // 合并选项
    mergeOptions: [
      { name: '按顺序合并', value: 'order' },
      { name: '按文件名排序', value: 'name' },
      { name: '按文件大小排序', value: 'size' },
      { name: '按修改时间排序', value: 'time' }
    ],
    selectedOption: 0,

    // 高级选项
    removeCertSign: false,
    generateToc: false,

    // 处理状态
    isProcessing: false,
    progress: 0,
    progressText: '准备中...',



    // UI状态
    showHelp: false,

    // 配置
    maxFileSize: 500 * 1024 * 1024, // 500MB
    maxFileCount: 10
  },

  onLoad(options) {
    console.log('合并页面加载成功');
    this.initPage();
  },

  onShow() {
    console.log('合并页面显示');
  },

  onReady() {
    console.log('合并页面准备完成');
    wx.showToast({
      title: '页面加载成功',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '合并PDF'
    });
  },

  /**
   * 选择文件
   */
  chooseFiles() {
    const that = this;
    wx.chooseMessageFile({
      count: this.data.maxFileCount,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        that.processSelectedFiles(res.tempFiles);
      },
      fail: (error) => {
        console.error('选择文件失败:', error);
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理选中的文件
   */
  processSelectedFiles(files) {
    if (!files || files.length === 0) {
      this.setData({ fileList: [] });
      return;
    }

    // 检查文件数量限制
    if (files.length > this.data.maxFileCount) {
      wx.showToast({
        title: `最多只能选择${this.data.maxFileCount}个文件`,
        icon: 'none'
      });
      return;
    }

    // 检查文件大小
    const oversizedFiles = files.filter(file => file.size > this.data.maxFileSize);
    if (oversizedFiles.length > 0) {
      const maxSizeMB = Math.round(this.data.maxFileSize / (1024 * 1024));
      wx.showToast({
        title: `文件大小不能超过${maxSizeMB}MB`,
        icon: 'none'
      });
      return;
    }

    // 处理文件信息
    const processedFiles = files.map((file, index) => {
      return {
        id: Date.now() + index,
        name: file.name,
        path: file.path,
        size: file.size,
        sizeText: this.formatFileSize(file.size),
        pages: Math.floor(Math.random() * 50) + 1, // 模拟页数
        type: 'pdf'
      };
    });

    this.setData({
      fileList: processedFiles,
      outputFileName: processedFiles.length > 1 ? '合并文件' :
                     (processedFiles[0]?.name?.replace('.pdf', '_合并') || '合并文件')
    });

    wx.showToast({
      title: `已添加${processedFiles.length}个文件`,
      icon: 'success'
    });
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 输入文件名
   */
  onFileNameInput(e) {
    this.setData({
      outputFileName: e.detail.value
    });
  },

  /**
   * 合并模式改变
   */
  onMergeModeChange(e) {
    this.setData({
      selectedMergeOption: parseInt(e.detail.value)
    });
  },

  /**
   * 切换高级选项显示
   */
  toggleAdvancedOptions() {
    this.setData({
      showAdvancedOptions: !this.data.showAdvancedOptions
    });
  },

  /**
   * 移除数字签名选项改变
   */
  onRemoveCertSignChange(e) {
    this.setData({
      removeCertSign: e.detail.value
    });
  },

  /**
   * 生成目录选项改变
   */
  onGenerateTocChange(e) {
    this.setData({
      generateToc: e.detail.value
    });
  },

  /**
   * 移除文件
   */
  removeFile(e) {
    const index = e.currentTarget.dataset.index;
    const fileList = this.data.fileList.filter((_, i) => i !== index);
    this.setData({ fileList });

    wx.showToast({
      title: '文件已移除',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 清空文件
   */
  clearFiles() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有文件吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ fileList: [] });
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 排序文件
   */
  sortFiles() {
    wx.showActionSheet({
      itemList: ['按文件名排序', '按文件大小排序', '按添加时间排序'],
      success: (res) => {
        const fileList = [...this.data.fileList];
        switch (res.tapIndex) {
          case 0: // 按文件名
            fileList.sort((a, b) => a.name.localeCompare(b.name));
            break;
          case 1: // 按文件大小
            fileList.sort((a, b) => b.size - a.size);
            break;
          case 2: // 按添加时间
            fileList.sort((a, b) => a.id - b.id);
            break;
        }
        this.setData({ fileList });
        wx.showToast({
          title: '排序完成',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 预览文件
   */
  previewFile(e) {
    const index = e.currentTarget.dataset.index;
    const file = this.data.fileList[index];

    wx.openDocument({
      filePath: file.path,
      fileType: 'pdf',
      success: () => {
        console.log('预览成功');
      },
      fail: (error) => {
        console.error('预览失败:', error);
        wx.showToast({
          title: '预览功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 输入文件名
   */
  onFileNameInput(e) {
    this.setData({
      outputFileName: e.detail.value
    });
  },

  /**
   * 合并模式改变
   */
  onModeChange(e) {
    this.setData({
      selectedOption: parseInt(e.detail.value)
    });
  },

  /**
   * 移除数字签名选项改变
   */
  onRemoveCertSignChange(e) {
    this.setData({
      removeCertSign: e.detail.value
    });
  },

  /**
   * 生成目录选项改变
   */
  onGenerateTocChange(e) {
    this.setData({
      generateToc: e.detail.value
    });
  },

  /**
   * 清空所有文件
   */
  clearAllFiles() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有文件吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ fileList: [] });
        }
      }
    });
  },

  /**
   * 排序文件
   */
  sortFiles() {
    wx.showActionSheet({
      itemList: ['按文件名排序', '按文件大小排序', '按添加时间排序'],
      success: (res) => {
        const fileList = [...this.data.fileList];
        switch (res.tapIndex) {
          case 0: // 按文件名
            fileList.sort((a, b) => a.name.localeCompare(b.name));
            break;
          case 1: // 按文件大小
            fileList.sort((a, b) => b.size - a.size);
            break;
          case 2: // 按添加时间
            fileList.sort((a, b) => a.id - b.id);
            break;
        }
        this.setData({ fileList });
      }
    });
  },

  /**
   * 预览文件
   */
  previewFile(e) {
    const index = e.currentTarget.dataset.index;
    const file = this.data.fileList[index];

    wx.openDocument({
      filePath: file.path,
      fileType: 'pdf',
      success: () => {
        console.log('预览成功');
      },
      fail: (error) => {
        console.error('预览失败:', error);
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示帮助
   */
  showHelp() {
    this.setData({ showHelpModal: true });
  },

  /**
   * 隐藏帮助
   */
  hideHelp() {
    this.setData({ showHelpModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 移除文件
  removeFile(e) {
    const index = e.currentTarget.dataset.index;
    const fileList = this.data.fileList.filter((_, i) => i !== index);
    this.setData({ fileList });
    
    wx.showToast({
      title: '文件已移除',
      icon: 'success'
    });
  },

  // 清空所有文件
  clearAllFiles() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有文件吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            fileList: [],
            outputFileName: '合并文件'
          });
          wx.showToast({
            title: '已清空所有文件',
            icon: 'success'
          });
        }
      }
    });
  },

  // 排序文件
  sortFiles() {
    const actions = ['按文件名排序', '按大小排序', '按添加时间排序'];
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        let fileList = [...this.data.fileList];
        switch (res.tapIndex) {
          case 0: // 按文件名
            fileList.sort((a, b) => a.name.localeCompare(b.name));
            break;
          case 1: // 按大小
            fileList.sort((a, b) => b.size - a.size);
            break;
          case 2: // 按添加时间
            fileList.sort((a, b) => a.id - b.id);
            break;
        }
        this.setData({ fileList });
        wx.showToast({
          title: '排序完成',
          icon: 'success'
        });
      }
    });
  },

  // 预览文件
  previewFile(e) {
    const index = e.currentTarget.dataset.index;
    const file = this.data.fileList[index];
    
    wx.openDocument({
      filePath: file.path,
      fileType: 'pdf',
      success: () => {
        console.log('预览文件成功');
      },
      fail: (error) => {
        console.error('预览文件失败:', error);
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 输入文件名
  onFileNameInput(e) {
    this.setData({
      outputFileName: e.detail.value
    });
  },

  // 合并模式改变
  onMergeModeChange(e) {
    this.setData({
      mergeMode: parseInt(e.detail.value)
    });
  },

  // 书签选项改变
  onBookmarkChange(e) {
    this.setData({
      keepBookmarks: e.detail.value
    });
  },

  // 优化选项改变
  onOptimizeChange(e) {
    this.setData({
      optimizeSize: e.detail.value
    });
  },

  /**
   * 开始合并
   */
  startMerge() {
    // 验证输入
    if (this.data.fileList.length < 2) {
      wx.showToast({
        title: '至少需要2个文件',
        icon: 'none'
      });
      return;
    }

    if (!this.data.outputFileName.trim()) {
      wx.showToast({
        title: '请输入文件名',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isProcessing: true,
      progress: 0,
      progressText: '准备合并...'
    });

    // 开始真实合并过程
    this.realMergeProcess();
  },

  /**
   * 真实合并过程
   */
  async realMergeProcess() {
    try {
      // 准备文件路径数组
      const filePaths = this.data.fileList.map(file => file.path);

      // 准备合并选项
      const mergeOptions = {
        sortType: this.getMergeSortType(),
        removeCertSign: this.data.removeCertSign,
        generateToc: this.data.generateToc
      };

      // 更新进度
      this.setData({
        progress: 10,
        progressText: '准备合并文件...'
      });

      // ✅ 使用微信专用API进行合并
      const result = await PDFService.mergePDFsWechat(filePaths, mergeOptions, (progressRes) => {
        // 处理进度回调
        if (progressRes.progress) {
          this.setData({
            progress: Math.min(progressRes.progress, 95),
            progressText: `合并中 ${progressRes.progress}%`
          });
        }
      });

      // 合并成功
      this.setData({
        progress: 100,
        progressText: '合并完成！'
      });

      // 处理合并结果
      this.handleMergeSuccess(result);

    } catch (error) {
      console.error('合并失败:', error);
      this.handleMergeError(error);
    }
  },

  /**
   * 获取合并排序类型
   */
  getMergeSortType() {
    const sortTypes = ['orderProvided', 'name', 'size', 'time'];
    return sortTypes[this.data.selectedOption] || 'orderProvided';
  },

  /**
   * 处理合并成功 - 使用统一结果页面
   */
  handleMergeSuccess(result) {
    console.log('=== 合并成功，准备跳转到结果页面 ===');
    console.log('result:', result);

    const totalPages = this.data.fileList.reduce((sum, file) => sum + file.pages, 0);

    // 重置处理状态
    this.setData({
      isProcessing: false,
      progress: 100,
      progressText: '合并完成！'
    });

    // 保存到历史记录
    const historyItem = {
      name: result.fileName || (this.data.outputFileName + '.pdf'),
      size: result.fileSize || 0,
      sizeText: this.formatFileSize(result.fileSize || 0),
      pages: totalPages,
      createTime: new Date().toLocaleString(),
      filePath: result.tempFilePath || result.filePath,
      success: true
    };
    this.saveToHistory(historyItem);

    // 使用 ResultHelper 跳转到统一结果页面
    const ResultHelper = require('../../utils/result-helper.js');
    ResultHelper.navigateToResult('merge', {
      fileName: result.fileName || (this.data.outputFileName + '.pdf'),
      fileData: result.fileData,
      fileSize: result.fileSize,
      processingTime: result.processingTime,
      pageCount: totalPages + '页'
    }, {
      sourcePage: 'pages/merge/merge'
    });

    // 震动反馈
    wx.vibrateShort();
  },

  /**
   * 处理合并错误
   */
  handleMergeError(error) {
    this.setData({
      isProcessing: false,
      progress: 0,
      progressText: '合并失败'
    });

    const errorMessage = error.message || '合并过程中发生错误';

    wx.showModal({
      title: '合并失败',
      content: errorMessage,
      showCancel: true,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          setTimeout(() => {
            this.startMerge();
          }, 500);
        }
      }
    });
  },



  // 保存到历史记录
  saveToHistory(result) {
    try {
      let history = wx.getStorageSync('mergeHistory') || [];
      const historyItem = {
        id: Date.now(),
        type: 'merge',
        result,
        fileCount: this.data.fileList.length,
        settings: {
          mergeMode: this.data.mergeMode,
          keepBookmarks: this.data.keepBookmarks,
          optimizeSize: this.data.optimizeSize
        },
        createTime: new Date().toISOString()
      };
      
      history.unshift(historyItem);
      history = history.slice(0, 50); // 最多保存50条记录
      
      wx.setStorageSync('mergeHistory', history);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },



  /**
   * 保存到历史记录
   */
  saveToHistory(mergeResult) {
    try {
      let history = wx.getStorageSync('mergeHistory') || [];

      const historyItem = {
        id: Date.now(),
        type: 'merge',
        name: mergeResult.name,
        fileCount: this.data.fileList.length,
        totalSize: mergeResult.sizeText,
        totalPages: mergeResult.pages,
        createTime: new Date().toISOString(),
        result: mergeResult
      };

      history.unshift(historyItem);
      history = history.slice(0, 50); // 最多保存50条记录

      wx.setStorageSync('mergeHistory', history);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },



  /**
   * 重置页面
   */
  resetPage() {
    this.setData({
      fileList: [],
      outputFileName: '合并文件',
      selectedMergeOption: 0,
      removeCertSign: false,
      generateToc: false,
      isProcessing: false,
      progress: 0,
      progressText: '',
      showHelpModal: false
    });
  },

  // 显示帮助
  showHelp() {
    this.setData({ showHelpModal: true });
  },

  // 隐藏帮助
  hideHelp() {
    this.setData({ showHelpModal: false });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭模态框
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'PDF合并工具 - 快速合并多个PDF文件',
      path: '/pages/merge/merge',
      imageUrl: '/assets/images/share-merge.jpg'
    };
  }
});
