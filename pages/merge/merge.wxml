<!--pages/merge/merge.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">📄 合并PDF</view>
    <view class="help-btn" bindtap="showHelp">❓</view>
  </view>

  <!-- 文件上传区域 -->
  <view class="upload-area" wx:if="{{fileList.length === 0}}" bindtap="chooseFiles">
    <view class="upload-icon">📁</view>
    <view class="upload-text">点击选择PDF文件</view>
    <view class="upload-tip">支持多个PDF文件，最大500MB</view>
    <view class="upload-features">
      <view class="feature-item">
        <text class="feature-icon">📊</text>
        <text class="feature-text">智能合并</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🔒</text>
        <text class="feature-text">安全可靠</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">⚡</text>
        <text class="feature-text">快速处理</text>
      </view>
    </view>
  </view>

  <!-- 文件列表 -->
  <view class="file-list" wx:if="{{fileList.length > 0}}">
    <view class="list-header">
      <view class="list-title">
        <text class="list-icon">📋</text>
        <text>已选择 {{fileList.length}} 个文件</text>
      </view>
      <view class="add-btn" bindtap="chooseFiles">
        <text class="add-icon">➕</text>
        <text>添加更多</text>
      </view>
    </view>

    <!-- 文件拖拽提示 -->
    <view class="drag-tip" wx:if="{{fileList.length > 1}}">
      <text class="tip-icon">👆</text>
      <text class="tip-text">长按拖拽调整文件顺序</text>
    </view>

    <view class="file-item" wx:for="{{fileList}}" wx:key="id" data-index="{{index}}">
      <view class="file-order">{{index + 1}}</view>
      <view class="file-icon">📄</view>
      <view class="file-info">
        <view class="file-name">{{item.name}}</view>
        <view class="file-details">
          <text class="file-size">{{item.sizeText}}</text>
          <text class="file-pages">{{item.pages}}页</text>
          <text class="file-status" wx:if="{{item.loading}}">分析中...</text>
        </view>
      </view>
      <view class="file-actions">
        <view class="action-btn preview-btn" bindtap="previewFile" data-index="{{index}}">
          <text class="action-icon">👁️</text>
        </view>
        <view class="action-btn delete-btn" bindtap="removeFile" data-index="{{index}}">
          <text class="action-icon">🗑️</text>
        </view>
      </view>
    </view>

    <view class="list-actions">
      <button class="action-btn-secondary" bindtap="clearFiles">
        <text class="btn-icon">🧹</text>
        <text>清空全部</text>
      </button>
      <button class="action-btn-secondary" bindtap="sortFiles">
        <text class="btn-icon">🔄</text>
        <text>重新排序</text>
      </button>
    </view>
  </view>

  <!-- 合并设置 -->
  <view class="settings" wx:if="{{fileList.length > 1}}">
    <view class="settings-header">
      <text class="settings-icon">⚙️</text>
      <text class="settings-title">合并设置</text>
    </view>

    <view class="setting-item">
      <view class="setting-label">
        <text class="label-icon">📝</text>
        <text>输出文件名</text>
      </view>
      <input class="setting-input"
             placeholder="请输入文件名"
             value="{{outputFileName}}"
             bindinput="onFileNameInput"
             maxlength="50" />
    </view>

    <view class="setting-item">
      <view class="setting-label">
        <text class="label-icon">🔀</text>
        <text>合并方式</text>
      </view>
      <picker range="{{mergeOptions}}"
              range-key="name"
              value="{{selectedOption}}"
              bindchange="onModeChange">
        <view class="picker-view">
          <text>{{mergeOptions[selectedOption].name}}</text>
          <text class="picker-arrow">⬇️</text>
        </view>
      </picker>
    </view>

    <view class="setting-item">
      <view class="setting-label">
        <text class="label-icon">🔐</text>
        <text>移除数字签名</text>
        <text class="setting-desc">移除原文件的数字签名信息</text>
      </view>
      <switch class="setting-switch"
             checked="{{removeCertSign}}"
             bindchange="onRemoveCertSignChange"
             color="#6366f1" />
    </view>

    <view class="setting-item">
      <view class="setting-label">
        <text class="label-icon">📑</text>
        <text>生成目录</text>
        <text class="setting-desc">为合并后的文件自动生成目录</text>
      </view>
      <switch class="setting-switch"
             checked="{{generateToc}}"
             bindchange="onGenerateTocChange"
             color="#6366f1" />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions" wx:if="{{fileList.length > 1}}">
    <button class="merge-btn"
            bindtap="startMerge"
            disabled="{{isProcessing}}"
            hover-class="merge-btn-hover">
      <text class="btn-icon" wx:if="{{!isProcessing}}">🚀</text>
      <text class="btn-icon rotating" wx:if="{{isProcessing}}">⚙️</text>
      <text class="btn-text">{{isProcessing ? '合并中...' : '开始合并'}}</text>
    </button>
  </view>

  <!-- 进度显示 -->
  <view class="progress" wx:if="{{isProcessing}}">
    <view class="progress-header">
      <text class="progress-icon rotating">⚙️</text>
      <text class="progress-title">正在合并PDF文件</text>
    </view>

    <view class="progress-info">
      <text class="progress-text">{{progressText || '准备中...'}}</text>
      <text class="progress-percent">{{progress}}%</text>
    </view>

    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>

    <view class="progress-steps">
      <view class="step-item {{progress >= 20 ? 'active' : ''}}">
        <text class="step-icon">📤</text>
        <text class="step-text">上传文件</text>
      </view>
      <view class="step-item {{progress >= 50 ? 'active' : ''}}">
        <text class="step-icon">🔄</text>
        <text class="step-text">处理中</text>
      </view>
      <view class="step-item {{progress >= 80 ? 'active' : ''}}">
        <text class="step-icon">📋</text>
        <text class="step-text">合并文件</text>
      </view>
      <view class="step-item {{progress >= 100 ? 'active' : ''}}">
        <text class="step-icon">✅</text>
        <text class="step-text">完成</text>
      </view>
    </view>
  </view>


</view>

<!-- 帮助弹窗 -->
<view class="modal" wx:if="{{showHelp}}" bindtap="hideHelp">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">
        <text class="modal-icon">❓</text>
        <text class="modal-text">使用帮助</text>
      </view>
      <text class="close-btn" bindtap="hideHelp">❌</text>
    </view>

    <view class="modal-body">
      <view class="help-section">
        <view class="help-title">
          <text class="help-icon">📋</text>
          <text>操作步骤</text>
        </view>
        <view class="help-steps">
          <view class="help-step">
            <text class="step-number">1</text>
            <text class="step-text">点击上传区域选择多个PDF文件</text>
          </view>
          <view class="help-step">
            <text class="step-number">2</text>
            <text class="step-text">长按拖拽调整文件顺序（可选）</text>
          </view>
          <view class="help-step">
            <text class="step-number">3</text>
            <text class="step-text">设置输出文件名和合并选项</text>
          </view>
          <view class="help-step">
            <text class="step-number">4</text>
            <text class="step-text">点击"开始合并"完成操作</text>
          </view>
        </view>
      </view>

      <view class="help-section">
        <view class="help-title">
          <text class="help-icon">📄</text>
          <text>文件要求</text>
        </view>
        <view class="help-content">
          <view class="help-item">• 支持标准PDF文件格式</view>
          <view class="help-item">• 单个文件最大500MB</view>
          <view class="help-item">• 最多可选择10个文件</view>
          <view class="help-item">• 建议使用英文文件名</view>
        </view>
      </view>

      <view class="help-section">
        <view class="help-title">
          <text class="help-icon">⚠️</text>
          <text>注意事项</text>
        </view>
        <view class="help-content">
          <view class="help-item">• 文件将按列表顺序进行合并</view>
          <view class="help-item">• 合并过程中请勿关闭页面</view>
          <view class="help-item">• 合并后文件自动保存到本地</view>
          <view class="help-item">• 支持移除数字签名和生成目录</view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn" bindtap="hideHelp">我知道了</button>
    </view>
  </view>
</view>
