/* pages/merge/merge.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 20rpx;
  /* 为底部TabBar预留空间 */
  padding-bottom: calc(20rpx + 88px + env(safe-area-inset-bottom));
}

/* 页面头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
}

.help-btn {
  font-size: 32rpx;
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

/* 文件上传区域 */
.upload-area {
  background: white;
  border: 3rpx dashed #d1d5db;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}

.upload-area:active {
  border-color: #6366f1;
  background-color: #f8fafc;
}

.upload-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 32rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 30rpx;
}

/* 上传区域特性 */
.upload-features {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-top: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 32rpx;
}

.feature-text {
  font-size: 20rpx;
  color: #6b7280;
}

/* 文件列表 */
.file-list {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.list-icon {
  font-size: 24rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #6366f1;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #6366f1;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.add-btn:active {
  background-color: #6366f1;
  color: white;
}

.add-icon {
  font-size: 20rpx;
}

/* 拖拽提示 */
.drag-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #f0f9ff;
  border: 1rpx solid #bae6fd;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.tip-icon {
  font-size: 20rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #0369a1;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  transition: all 0.3s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:active {
  background-color: #f9fafb;
}

.file-order {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6366f1;
  color: white;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: 600;
  margin-right: 16rpx;
}

.file-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  color: #111827;
  font-weight: 500;
  margin-bottom: 8rpx;
  word-break: break-all;
  line-height: 1.4;
}

.file-details {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.file-size, .file-pages {
  font-size: 22rpx;
  color: #6b7280;
}

.file-status {
  font-size: 22rpx;
  color: #f59e0b;
}

.file-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.preview-btn {
  background: #f0f9ff;
}

.delete-btn {
  background: #fef2f2;
}

.action-icon {
  font-size: 24rpx;
}

.list-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}

.action-btn-secondary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.action-btn-secondary:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 20rpx;
}

/* 设置区域 */
.settings {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.settings-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.settings-icon {
  font-size: 24rpx;
}

.settings-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.setting-item {
  margin-bottom: 24rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.label-icon {
  font-size: 20rpx;
}

.setting-label text {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
}

.setting-desc {
  font-size: 22rpx;
  color: #6b7280;
  margin-top: 4rpx;
}

.setting-input {
  width: 100%;
  padding: 16rpx;
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 26rpx;
  background: #f9fafb;
  transition: border-color 0.3s ease;
}

.setting-input:focus {
  border-color: #6366f1;
  background: white;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  background: #f9fafb;
  font-size: 26rpx;
  color: #374151;
  transition: all 0.3s ease;
}

.picker-view:active {
  background: #f3f4f6;
}

.picker-arrow {
  font-size: 20rpx;
  color: #9ca3af;
}

.setting-switch {
  transform: scale(1.2);
}

/* 操作按钮 */
.actions {
  margin-bottom: 30rpx;
}

.merge-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.merge-btn:disabled {
  background: #9ca3af;
  box-shadow: none;
}

.merge-btn-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(99, 102, 241, 0.4);
}

.btn-text {
  font-size: 28rpx;
}

/* 进度显示 */
.progress {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.progress-icon {
  font-size: 32rpx;
  color: #6366f1;
}

.progress-title {
  font-size: 28rpx;
  color: #374151;
  font-weight: 600;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #6b7280;
}

.progress-percent {
  font-size: 26rpx;
  color: #6366f1;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  transition: width 0.5s ease;
}

/* 进度步骤 */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.step-item.active {
  opacity: 1;
}

.step-icon {
  font-size: 24rpx;
}

.step-text {
  font-size: 20rpx;
  color: #6b7280;
}



/* 帮助弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f9fafb;
  border-bottom: 1rpx solid #e5e7eb;
}

.modal-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
}

.close-btn {
  font-size: 28rpx;
  padding: 8rpx;
}

.modal-body {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24rpx;
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
}

.help-step, .help-content {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.help-step:last-child, .help-content:last-child {
  margin-bottom: 0;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 20rpx;
}

.upload-area {
  border: 4rpx dashed #d1d5db;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  background-color: #ffffff;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.upload-title {
  font-size: 32rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.upload-desc {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}

.upload-info {
  font-size: 22rpx;
  color: #9ca3af;
  margin-bottom: 30rpx;
}

.upload-btn {
  padding: 20rpx 40rpx;
  background-color: #6366f1;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

/* 文件列表 */
.file-section {
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.add-more-btn {
  color: #6366f1;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #6366f1;
  border-radius: 6rpx;
}

.file-list {
  background-color: white;
  border-radius: 12rpx;
  padding: 16rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 26rpx;
  color: #111827;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.file-size {
  font-size: 22rpx;
  color: #6b7280;
}

.file-actions {
  display: flex;
  gap: 12rpx;
}

.file-actions text {
  font-size: 24rpx;
  padding: 8rpx;
}

.file-actions-bar {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.file-actions-bar button {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 1rpx solid #e5e7eb;
  background-color: white;
}

/* 设置区域 */
.settings-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.setting-item {
  margin-bottom: 20rpx;
}

.setting-label {
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 8rpx;
}

.setting-input {
  width: 100%;
  padding: 16rpx;
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #f9fafb;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #e5e7eb;
  border-radius: 8rpx;
  background-color: #f9fafb;
  font-size: 26rpx;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 20rpx;
}

.merge-btn {
  width: 100%;
  padding: 24rpx;
  background-color: #6366f1;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.merge-btn:disabled {
  background-color: #9ca3af;
}

/* 进度显示 */
.progress-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.progress-fill {
  height: 100%;
  background-color: #6366f1;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 22rpx;
  color: #6b7280;
}

/* 结果显示 */
.result-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12rpx;
}

.result-desc {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 24rpx;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: #f9fafb;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
}

.file-preview text {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.result-actions {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.result-actions button {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 1rpx solid #e5e7eb;
  background-color: white;
}

.new-task button {
  width: 100%;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 1rpx solid #6366f1;
  color: #6366f1;
  background-color: white;
}

/* 帮助弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f9fafb;
  border-bottom: 1rpx solid #e5e7eb;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.modal-body {
  padding: 20rpx;
}

.help-item {
  margin-bottom: 20rpx;
}

.help-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
}

.help-content, .help-steps {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

.help-step {
  margin-bottom: 6rpx;
}

/* 按钮样式 */
.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s;
}

.btn:active {
  transform: scale(0.98);
}

.btn-primary {
  background-color: #6366f1;
  color: #ffffff;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-outline {
  background-color: transparent;
  color: #6366f1;
  border: 2rpx solid #6366f1;
}

.btn-large {
  height: 100rpx;
  font-size: 32rpx;
}

/* 设置区域 */
.settings-section {
  margin: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.setting-item {
  margin-bottom: 30rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 8rpx;
}

.setting-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f9fafb;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background-color: #f9fafb;
  font-size: 28rpx;
}

.setting-switch {
  transform: scale(1.2);
}

/* 操作区域 */
.action-section {
  margin: 30rpx;
}

/* 进度显示 */
.progress-section {
  margin: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.progress-icon {
  font-size: 80rpx;
  color: #6366f1;
  margin-bottom: 24rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 24rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background-color: #6366f1;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 24rpx;
  color: #6b7280;
}

/* 结果显示 */
.result-section {
  margin: 30rpx;
}

.result-content {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
}

.result-icon.success {
  color: #10b981;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16rpx;
}

.result-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
}

.result-file {
  margin-bottom: 40rpx;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f9fafb;
  border-radius: 12rpx;
  text-align: left;
}

.file-preview .iconfont {
  font-size: 60rpx;
  color: #dc2626;
  margin-right: 24rpx;
}

.result-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.new-task {
  padding-top: 30rpx;
  border-top: 1rpx solid #e5e7eb;
}

/* 帮助弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f9fafb;
  border-bottom: 1rpx solid #e5e7eb;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
}

.close-btn {
  font-size: 40rpx;
  color: #6b7280;
  padding: 8rpx;
}

.modal-body {
  padding: 30rpx;
}

.help-item {
  margin-bottom: 30rpx;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.help-content {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

.help-steps {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

.help-step {
  margin-bottom: 8rpx;
}

/* 动画效果 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 16rpx;
  }

  .header {
    padding: 24rpx;
    margin-bottom: 24rpx;
  }

  .title {
    font-size: 32rpx;
  }

  .upload-area {
    padding: 60rpx 30rpx;
  }

  .file-list {
    padding: 20rpx;
  }

  .settings {
    padding: 20rpx;
  }
}
