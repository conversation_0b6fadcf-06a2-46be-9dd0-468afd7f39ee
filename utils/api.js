// API客户端封装
// 统一管理与Stirling-PDF后端的所有通信

const { config, getApiUrl, get } = require('../config/app.config.js');

/**
 * API客户端类
 */
class APIClient {
  constructor() {
    this.baseURL = get('api.baseURL');
    this.version = get('api.version');
    this.timeout = get('api.timeout');
    this.apiKey = get('api.apiKey');
    this.retryTimes = get('api.retry.times');
    this.retryDelay = get('api.retry.delay');
  }

  /**
   * 发送HTTP请求的通用方法
   */
  async request(endpoint, options = {}) {
    const url = getApiUrl(endpoint);
    const defaultOptions = {
      timeout: this.timeout,
      header: {
        'Content-Type': 'multipart/form-data',
        ...(this.apiKey && { 'X-API-KEY': this.apiKey })
      }
    };

    const requestOptions = {
      ...defaultOptions,
      ...options,
      url,
      header: {
        ...defaultOptions.header,
        ...options.header
      }
    };

    return this.requestWithRetry(requestOptions);
  }

  /**
   * 带重试机制的请求方法
   */
  async requestWithRetry(options, retryCount = 0) {
    console.log('发送请求:', options);

    return new Promise((resolve, reject) => {
      wx.uploadFile({
        ...options,
        success: (res) => {
          console.log('请求响应:', {
            statusCode: res.statusCode,
            dataType: typeof res.data,
            dataLength: res.data ? res.data.length : 0,
            header: res.header,
            contentType: res.header['content-type'] || res.header['Content-Type']
          });

          // 检查响应类型，如果是PDF文件，需要特殊处理
          const contentType = res.header['content-type'] || res.header['Content-Type'] || '';
          console.log('响应内容类型:', contentType);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(this.handleResponse(res));
          } else {
            console.error('请求失败，状态码:', res.statusCode);
            console.error('响应数据:', res.data);

            const error = this.handleError(res);
            if (retryCount < this.retryTimes && this.shouldRetry(res.statusCode)) {
              setTimeout(() => {
                this.requestWithRetry(options, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              }, this.retryDelay * (retryCount + 1));
            } else {
              reject(error);
            }
          }
        },
        fail: (error) => {
          console.error('=== 网络请求失败详情 ===');
          console.error('请求失败:', error);
          console.error('请求URL:', options.url);
          console.error('请求方法:', options.method);
          console.error('错误类型:', error.errMsg);
          console.error('是否超时:', error.errMsg && error.errMsg.includes('timeout'));

          // 检查网络状态
          if (wx.getNetworkType) {
            wx.getNetworkType({
              success: (res) => {
                console.error('当前网络类型:', res.networkType);
                console.error('网络是否可用:', res.networkType !== 'none');
              }
            });
          }

          if (retryCount < this.retryTimes) {
            console.log(`准备第${retryCount + 1}次重试，延迟${this.retryDelay * (retryCount + 1)}ms...`);
            setTimeout(() => {
              this.requestWithRetry(options, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, this.retryDelay * (retryCount + 1));
          } else {
            console.error('重试次数已用完，请求最终失败');
            reject(this.handleError(error));
          }
        }
      });
    });
  }

  /**
   * 处理PDF响应数据
   */
  handlePDFResponse(response) {
    console.log('处理PDF响应数据:', {
      statusCode: response.statusCode,
      contentType: response.header['content-type'] || response.header['Content-Type'],
      dataType: typeof response.data,
      dataLength: response.data ? response.data.length : 'undefined'
    });

    // 对于PDF响应，数据应该是二进制字符串
    // 我们需要将其转换为正确的格式
    try {
      // 检查数据是否是字符串格式的二进制数据
      if (typeof response.data === 'string') {
        console.log('PDF数据是字符串格式，长度:', response.data.length);

        // 检查是否包含PDF头
        if (response.data.startsWith('%PDF')) {
          console.log('检测到PDF头，数据格式正确');

          // 创建一个虚拟的文件名
          const fileName = `processed_${Date.now()}.pdf`;

          return {
            ...response,
            tempFilePath: fileName,
            savedFilePath: fileName,
            fileName: fileName,
            fileData: response.data,
            isDownloaded: true
          };
        } else {
          console.warn('数据不包含PDF头，可能是错误响应');
          // 尝试解析为JSON错误响应
          try {
            const errorData = JSON.parse(response.data);
            throw new Error(errorData.message || '服务器返回错误');
          } catch (parseError) {
            throw new Error('无效的PDF数据');
          }
        }
      } else {
        throw new Error('PDF响应数据格式不正确');
      }
    } catch (error) {
      console.error('PDF响应处理失败:', error);
      throw error;
    }
  }

  /**
   * 处理响应数据
   */
  handleResponse(response) {
    console.log('处理响应数据:', {
      statusCode: response.statusCode,
      contentType: response.header['content-type'] || response.header['Content-Type'],
      dataType: typeof response.data,
      dataLength: response.data ? response.data.length : 'undefined'
    });

    const contentType = response.header['content-type'] || response.header['Content-Type'];

    if (contentType && contentType.includes('application/json')) {
      try {
        return {
          ...response,
          data: JSON.parse(response.data)
        };
      } catch (e) {
        console.warn('JSON解析失败:', e);
      }
    }

    // 处理文件下载响应
    if (contentType && (
      contentType.includes('application/zip') ||
      contentType.includes('application/octet-stream') ||
      contentType.includes('application/pdf') ||
      contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    )) {
      console.log('检测到文件下载响应，内容类型:', contentType);

      // 从Content-Disposition头中提取文件名
      const contentDisposition = response.header['content-disposition'] || response.header['Content-Disposition'];
      let fileName = 'download.pdf';
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = fileNameMatch[1].replace(/['"]/g, '');
          // URL解码文件名
          try {
            fileName = decodeURIComponent(fileName);
          } catch (e) {
            console.warn('文件名解码失败:', e);
          }
        }
      }

      // 生成文件信息
      const timestamp = Date.now();
      const fileExtension = this.getFileExtensionFromContentType(contentType);
      const virtualPath = `temp_${timestamp}${fileExtension}`;

      console.log('文件下载信息:', {
        fileName,
        virtualPath,
        fileSize: response.data ? response.data.length : 0
      });

      // 检查数据格式并进行适当处理
      let processedData = response.data;

      console.log('原始数据类型:', typeof response.data);
      console.log('原始数据长度:', response.data ? response.data.length : 0);

      // 显示数据的前100个字符用于调试
      if (typeof response.data === 'string' && response.data.length > 0) {
        console.log('数据前100字符:', response.data.substring(0, 100));

        // 检查是否有编码问题
        const firstBytes = [];
        for (let i = 0; i < Math.min(10, response.data.length); i++) {
          firstBytes.push(response.data.charCodeAt(i));
        }
        console.log('前10个字节的字符码:', firstBytes);
      }

      // 如果数据是字符串且看起来像JSON，可能需要解析
      if (typeof response.data === 'string') {
        try {
          // 检查是否是JSON包装的base64数据
          const parsed = JSON.parse(response.data);
          if (parsed && parsed.data) {
            processedData = parsed.data;
            console.log('解析JSON包装的数据');
            console.log('解析后数据长度:', processedData.length);
          }
        } catch (e) {
          // 不是JSON，保持原样
          console.log('数据不是JSON格式，保持原样');

          // 检查是否是base64格式
          if (response.data.match(/^[A-Za-z0-9+/]+=*$/)) {
            console.log('检测到base64格式数据');
          } else if (response.data.startsWith('%PDF')) {
            console.log('检测到PDF二进制数据');

            // 检查PDF数据是否有编码问题
            const pdfHeader = response.data.substring(0, 20);
            console.log('PDF头部详细:', pdfHeader);

            // 检查是否有非ASCII字符
            let hasNonAscii = false;
            for (let i = 0; i < Math.min(100, response.data.length); i++) {
              const charCode = response.data.charCodeAt(i);
              if (charCode > 127) {
                hasNonAscii = true;
                break;
              }
            }
            console.log('包含非ASCII字符:', hasNonAscii);

          } else {
            console.log('未知数据格式');
          }
        }
      }

      return {
        ...response,
        tempFilePath: virtualPath,
        savedFilePath: virtualPath,
        fileName: fileName,
        fileData: processedData,
        fileSize: processedData ? processedData.length : 0,
        contentType: contentType,
        isFileDownload: true
      };
    }

    return response;
  }

  /**
   * 根据内容类型获取文件扩展名
   */
  getFileExtensionFromContentType(contentType) {
    const typeMap = {
      'application/zip': '.zip',
      'application/pdf': '.pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/msword': '.doc',
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'application/octet-stream': '.zip'
    };

    for (const [type, ext] of Object.entries(typeMap)) {
      if (contentType.includes(type)) {
        return ext;
      }
    }

    return '.bin';
  }

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('API错误详情:', error);

    const errorMap = {
      400: '请求参数错误',
      401: '未授权访问，请检查API密钥',
      403: '权限不足',
      404: '请求的资源不存在',
      413: '文件过大，请选择较小的文件',
      415: '不支持的文件格式',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时'
    };

    const statusCode = error.statusCode || error.status || 0;
    let message = errorMap[statusCode] || error.errMsg || '网络请求失败';

    // 尝试从响应数据中获取更详细的错误信息
    if (error.data) {
      try {
        let errorData = error.data;
        if (typeof errorData === 'string') {
          errorData = JSON.parse(errorData);
        }
        if (errorData.message) {
          message = errorData.message;
        } else if (errorData.error) {
          message = errorData.error;
        } else if (errorData.details) {
          message = errorData.details;
        }

        // 对于400错误，提供更详细的参数错误信息
        if (statusCode === 400) {
          console.error('400错误详细信息:', errorData);
          if (errorData.errors && Array.isArray(errorData.errors)) {
            message = `参数错误: ${errorData.errors.join(', ')}`;
          } else if (typeof errorData === 'string' && errorData.includes('validation')) {
            message = `参数验证失败: ${errorData}`;
          }
        }
      } catch (e) {
        console.warn('解析错误响应数据失败:', e);
        // 对于400错误，如果无法解析响应，直接显示原始数据
        if (statusCode === 400 && error.data) {
          console.error('400错误原始响应:', error.data);
          message = `请求参数错误: ${error.data}`;
        }
      }
    }

    const errorResult = {
      code: statusCode,
      message,
      originalError: error
    };

    console.error('处理后的错误信息:', errorResult);
    return errorResult;
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(statusCode) {
    return statusCode >= 500 || statusCode === 0 || statusCode === 408;
  }

  /**
   * 测试服务器连接
   */
  async testConnection() {
    try {
      // 尝试访问Swagger文档页面来测试连接
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseURL}/`,
          method: 'GET',
          timeout: 10000,
          success: resolve,
          fail: reject
        });
      });

      console.log('服务器连接测试结果:', response);
      return response.statusCode === 200 || response.statusCode === 302;
    } catch (error) {
      console.error('服务器连接测试失败:', error);
      return false;
    }
  }

  /**
   * 上传文件的通用方法
   */
  async uploadFile(endpoint, filePath, formData = {}, onProgress = null) {
    console.log('uploadFile调用:', {
      endpoint,
      filePath,
      formData
    });

    const url = getApiUrl(endpoint);
    console.log('完整URL:', url);

    return new Promise((resolve, reject) => {
      // 检查文件路径
      console.log('准备上传文件:', {
        url,
        filePath,
        formData,
        fileExists: wx.getFileSystemManager().accessSync ? 'unknown' : 'checking...'
      });

      // 尝试验证文件是否存在
      try {
        const fs = wx.getFileSystemManager();
        const stats = fs.statSync(filePath);
        console.log('文件状态:', {
          size: stats.size,
          isFile: stats.isFile(),
          path: filePath
        });
      } catch (e) {
        console.warn('无法获取文件状态:', e);
      }

      const uploadTask = wx.uploadFile({
        url,
        filePath,
        name: 'fileInput', // Stirling-PDF期望的参数名
        formData,
        header: {
          ...(this.apiKey && { 'X-API-KEY': this.apiKey })
        },
        success: (res) => {
          console.log('上传成功响应:', res);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 特殊处理PDF文件响应
            const contentType = res.header['content-type'] || res.header['Content-Type'] || '';
            console.log('响应Content-Type:', contentType);

            if (contentType.includes('application/pdf')) {
              console.log('检测到PDF响应，进行二进制处理');
              // 对于PDF响应，需要特殊处理二进制数据
              resolve(this.handlePDFResponse(res));
            } else {
              resolve(this.handleResponse(res));
            }
          } else {
            console.error('上传失败，状态码:', res.statusCode);
            console.error('响应数据:', res.data);
            reject(this.handleError(res));
          }
        },
        fail: (error) => {
          console.error('上传失败:', error);
          reject(this.handleError(error));
        }
      });

      if (onProgress) {
        uploadTask.onProgressUpdate((progressInfo) => {
          console.log('上传进度信息:', progressInfo);
          // 微信小程序的进度回调格式：{ progress: 50, totalBytesSent: 1024, totalBytesExpectedToSend: 2048 }
          const progressPercent = progressInfo.progress || 0;
          onProgress(progressPercent);
        });
      }
    });
  }
}

// 创建全局API客户端实例
const apiClient = new APIClient();

/**
 * PDF转换服务
 */
const ConvertService = {
  // PDF转图片
  async pdfToImage(filePath, options = {}, onProgress = null) {
    const formData = {
      imageFormat: options.imageFormat || 'png',
      singleOrMultiple: options.singleOrMultiple || 'multiple',
      colorType: options.colorType || 'color',
      dpi: options.dpi || 300,
      pageNumbers: options.pageNumbers || ''
    };
    return apiClient.uploadFile('/convert/pdf/img', filePath, formData, onProgress);
  },

  // 图片转PDF
  async imageToPDF(filePaths, options = {}, onProgress = null) {
    // 确保所有参数都是字符串格式，符合表单数据要求
    const formData = {
      fitOption: String(options.fitOption || 'fillPage'),
      colorType: String(options.colorType || 'color'),
      autoRotate: String(options.autoRotate || false)
    };

    // 暂时只支持单张图片转PDF，多图片功能需要后端支持
    if (Array.isArray(filePaths) && filePaths.length > 1) {
      throw new Error('当前版本暂不支持多张图片合并转PDF，请选择单张图片或使用其他工具');
    }

    const filePath = Array.isArray(filePaths) ? filePaths[0] : filePaths;
    console.log('图片转PDF参数:', formData);
    console.log('文件路径:', filePath);

    return apiClient.uploadFile('/convert/img/pdf', filePath, formData, onProgress);
  },

  // Office文档转PDF
  async officeToPDF(filePath, onProgress = null) {
    return apiClient.uploadFile('/convert/file/pdf', filePath, {}, onProgress);
  },

  // PDF转Word
  async pdfToWord(filePath, options = {}, onProgress = null) {
    const formData = {
      outputFormat: options.outputFormat || 'docx'
    };
    console.log('PDF转Word API调用参数:', {
      endpoint: '/convert/pdf/word',
      filePath,
      formData
    });

    try {
      const result = await apiClient.uploadFile('/convert/pdf/word', filePath, formData, onProgress);
      console.log('PDF转Word API响应:', result);
      return result;
    } catch (error) {
      console.error('PDF转Word API调用失败:', error);
      throw error;
    }
  },

  // PDF转HTML
  async pdfToHtml(filePath, onProgress = null) {
    return apiClient.uploadFile('/convert/pdf/html', filePath, {}, onProgress);
  },

  // HTML转PDF
  async htmlToPDF(filePath, options = {}, onProgress = null) {
    const formData = {
      zoom: options.zoom || 1.0,
      pageWidth: options.pageWidth || 'A4',
      pageHeight: options.pageHeight || 'A4'
    };
    return apiClient.uploadFile('/convert/html/pdf', filePath, formData, onProgress);
  },

  // PDF转文本
  async pdfToText(filePath, onProgress = null) {
    return apiClient.uploadFile('/convert/pdf/txt', filePath, {}, onProgress);
  },

  // PDF转CSV
  async pdfToCSV(filePath, onProgress = null) {
    return apiClient.uploadFile('/convert/pdf/csv', filePath, {}, onProgress);
  },

  // PDF转XML
  async pdfToXML(filePath, onProgress = null) {
    return apiClient.uploadFile('/convert/pdf/xml', filePath, {}, onProgress);
  },

  // ========== 微信小程序专用转换API ==========

  // PDF转图片 - 微信小程序专用版本
  async pdfToImageWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API PDF转图片...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        imageFormat: options.imageFormat || 'png',
        singleOrMultiple: options.singleOrMultiple || 'multiple',
        colorType: options.colorType || 'color',
        dpi: options.dpi || 300,
        pageNumbers: options.pageNumbers || ''
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/pdf-to-image'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.${options.imageFormat || 'png'}`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF转图片失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // 图片转PDF - 微信小程序专用版本
  async imageToPDFWechat(filePaths, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API 图片转PDF...');

    try {
      const fs = wx.getFileSystemManager();
      const imageFiles = [];

      // 处理多个图片文件
      const paths = Array.isArray(filePaths) ? filePaths : [filePaths];
      for (const filePath of paths) {
        const fileBuffer = fs.readFileSync(filePath);
        const base64Data = wx.arrayBufferToBase64(fileBuffer);
        const fileName = filePath.split('/').pop();

        imageFiles.push({
          fileData: base64Data,
          fileName: fileName
        });
      }

      // 构建请求数据
      const requestData = {
        imageFiles: imageFiles,
        fitOption: options.fitOption || 'fillPage',
        colorType: options.colorType || 'color',
        autoRotate: options.autoRotate || false,
        outputFileName: options.outputFileName || 'converted_images.pdf'
      };

      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/image-to-pdf'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || '图片转PDF失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF转Word - 微信小程序专用版本
  async pdfToWordWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API PDF转Word...');

    try {
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        outputFormat: options.outputFormat || 'docx'
      };

      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/pdf-to-word'),
          method: 'POST',
          data: requestData,
          timeout: 120000, // 设置2分钟超时
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.${options.outputFormat || 'docx'}`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF转Word失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // Office转PDF - 微信小程序专用版本
  async officeToPDFWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API Office转PDF...');
    console.log('文件路径:', filePath);

    try {
      const fs = wx.getFileSystemManager();

      // 检查文件是否存在
      try {
        const stats = fs.statSync(filePath);
        console.log('文件信息:', {
          size: stats.size,
          isFile: stats.isFile(),
          path: filePath
        });

        // 文件大小预检查
        if (stats.size > 2 * 1024 * 1024) { // 2MB以上
          console.warn('文件较大:', stats.size, 'bytes，可能需要较长处理时间');
          // 可以在这里显示用户提示
        }

        if (stats.size > 10 * 1024 * 1024) { // 10MB以上
          throw new Error('文件过大，请使用PC端处理');
        }
      } catch (statError) {
        console.error('文件状态检查失败:', statError);
        throw new Error('文件不存在或无法访问: ' + filePath);
      }

      // 读取文件
      console.log('开始读取文件...');
      const fileBuffer = fs.readFileSync(filePath);
      console.log('文件读取成功，大小:', fileBuffer.byteLength, 'bytes');

      // 转换为base64
      console.log('开始base64编码...');
      const base64Data = wx.arrayBufferToBase64(fileBuffer);
      console.log('base64编码完成，长度:', base64Data.length);

      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || filePath.split('/').pop()
      };

      console.log('请求数据准备完成:', {
        fileName: requestData.fileName,
        dataLength: requestData.fileData.length
      });

      return new Promise((resolve, reject) => {
        console.log('发送请求到:', getApiUrl('/miniprogram/wechat/office-to-pdf'));

        // 设置合理的超时时间
        const timeout = 120000; // 2分钟应该足够了

        wx.request({
          url: getApiUrl('/miniprogram/wechat/office-to-pdf'),
          method: 'POST',
          data: requestData,
          timeout: timeout,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            console.log('请求成功，状态码:', res.statusCode);
            console.log('响应数据:', {
              success: res.data?.success,
              message: res.data?.message,
              fileName: res.data?.fileName,
              fileSize: res.data?.fileSize,
              processingTime: res.data?.processingTime,
              hasFileData: !!res.data?.fileData,
              fileDataLength: res.data?.fileData?.length
            });

            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'Office转PDF失败';
              console.error('转换失败:', errorMsg);
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            console.error('请求失败:', error);
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF转HTML - 微信小程序专用版本
  async pdfToHtmlWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API PDF转HTML...');

    try {
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf'
      };

      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/pdf-to-html'),
          method: 'POST',
          data: requestData,
          timeout: 120000, // 设置2分钟超时
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.zip`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF转HTML失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // HTML转PDF - 微信小程序专用版本
  async htmlToPDFWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API HTML转PDF...');

    try {
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || filePath.split('/').pop(),
        zoom: options.zoom || 1.0
      };

      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/html-to-pdf'),
          method: 'POST',
          data: requestData,
          timeout: 120000, // 设置2分钟超时
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'HTML转PDF失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  }
};

/**
 * PDF基础操作服务
 */
const PDFService = {
  // PDF合并 - 微信小程序专用版本
  async mergePDFsWechat(filePaths, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API合并PDF...');

    try {
      // 1. 处理多个文件
      const fs = wx.getFileSystemManager();
      const fileDataList = [];
      const fileNameList = [];

      // 读取所有文件并转换为base64
      for (let i = 0; i < filePaths.length; i++) {
        const filePath = filePaths[i];
        const fileBuffer = fs.readFileSync(filePath);
        const base64Data = wx.arrayBufferToBase64(fileBuffer);
        const fileName = `file_${i + 1}.pdf`;

        fileDataList.push(base64Data);
        fileNameList.push(fileName);
      }

      // 2. 构建请求数据
      const requestData = {
        fileDataList: fileDataList,
        fileNameList: fileNameList,
        outputFileName: options.outputFileName || 'merged.pdf',
        sortType: options.sortType || 'orderProvided',
        removeCertSign: options.removeCertSign || false,
        generateToc: options.generateToc || false
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/merge-pdfs'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF合并失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF合并 - 旧版本（保持兼容性）
  async mergePDFs(filePaths, options = {}, onProgress = null) {
    const formData = {
      sortType: options.sortType || 'orderProvided',
      removeCertSign: options.removeCertSign || false,
      generateToc: options.generateToc || false
    };

    // 对于多文件，使用第一个文件作为主文件
    if (Array.isArray(filePaths) && filePaths.length > 1) {
      formData.additionalFiles = JSON.stringify(filePaths.slice(1));
      return apiClient.uploadFile('/general/merge-pdfs', filePaths[0], formData, onProgress);
    } else {
      const filePath = Array.isArray(filePaths) ? filePaths[0] : filePaths;
      return apiClient.uploadFile('/general/merge-pdfs', filePath, formData, onProgress);
    }
  },

  // PDF分割 - 微信小程序专用版本
  async splitPDFWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API分割PDF...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        pageNumbers: options.pageNumbers || '1-',
        splitType: options.splitType || 'pages',
        outputFileName: options.outputFileName || 'split.pdf'
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/split-pdf'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF分割失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF分割 - 旧版本（保持兼容性）
  async splitPDF(filePath, options = {}, onProgress = null) {
    const formData = {
      pageNumbers: options.pageNumbers || '1-',
      splitType: options.splitType || 'pages'
    };
    return apiClient.uploadFile('/general/split-pages', filePath, formData, onProgress);
  },

  // PDF压缩 - 微信小程序专用版本
  async compressPDFWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API压缩PDF...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        optimizeLevel: options.optimizeLevel || 1,
        imageQuality: options.imageQuality || 0.8,
        optimizeImages: options.optimizeImages !== false,
        removeUnusedObjects: options.removeUnusedObjects !== false,
        outputFileName: options.outputFileName || 'compressed.pdf'
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/compress-pdf'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF压缩失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF压缩 - 旧版本（保持兼容性）
  async compressPDF(filePath, options = {}, onProgress = null) {
    const formData = {
      optimizeLevel: options.optimizeLevel || 1,
      imageQuality: options.imageQuality || 0.8
    };
    return apiClient.uploadFile('/misc/compress-pdf', filePath, formData, onProgress);
  },

  // PDF旋转 - 微信小程序专用版本
  async rotatePDFWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API旋转PDF...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        angle: options.angle || 90,  // 旋转角度，必须是90的倍数
        pageRange: options.pageRange || '',  // 页面范围，留空表示所有页面
        outputFileName: options.outputFileName || 'rotated.pdf'
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/rotate-pdf'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF旋转失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF删除页面 - 微信小程序专用版本
  async removePagesWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API删除PDF页面...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        pagesToDelete: options.pagesToDelete || '1',  // 要删除的页面，如 "1,3,5" 或 "1-3,5"
        outputFileName: options.outputFileName || 'removed_pages.pdf'
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/remove-pages'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF删除页面失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF重排页面 - 微信小程序专用版本（用于提取页面）
  async rearrangePagesWechat(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API重排PDF页面...');

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'document.pdf',
        pageOrder: options.pageOrder || '1',  // 页面顺序，如 "1,3,5" 表示只保留这些页面
        outputFileName: options.outputFileName || 'rearranged.pdf'
      };

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/rearrange-pages'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve({
                fileData: res.data.fileData,
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              const errorMsg = res.data?.message || 'PDF重排页面失败';
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // PDF旋转
  async rotatePDF(filePath, options = {}, onProgress = null) {
    const formData = {
      angle: options.angle || 90,
      pageNumbers: options.pageNumbers || 'all'
    };
    return apiClient.uploadFile('/general/rotate-pdf', filePath, formData, onProgress);
  },

  // 提取页面
  async extractPages(filePath, options = {}, onProgress = null) {
    const formData = {
      pageNumbers: options.pageNumbers || '1'
    };
    return apiClient.uploadFile('/general/extract-pages', filePath, formData, onProgress);
  },

  // 删除页面
  async removePages(filePath, options = {}, onProgress = null) {
    const formData = {
      pageNumbers: options.pageNumbers || ''
    };
    return apiClient.uploadFile('/general/remove-pages', filePath, formData, onProgress);
  }
};

/**
 * PDF安全操作服务
 */
const SecurityService = {
  // 添加密码保护 - 微信小程序专用版本
  async addPassword(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API添加密码保护...');
    console.log('文件路径:', filePath);
    console.log('选项:', options);

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      console.log('文件转换为base64成功，长度:', base64Data.length);

      // 2. 构建请求数据，匹配后端WechatPasswordRequest模型
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'upload.pdf',
        password: options.userPassword || options.password || '',
        ownerPassword: options.ownerPassword || '',
        keyLength: options.keyLength || 256,
        allowPrinting: options.allowPrinting !== false,
        allowCopy: options.allowCopy !== false,
        allowModify: options.allowModify !== false,
        allowAnnotations: options.allowAnnotations !== false,
        allowFormFilling: options.allowFormFilling !== false
      };

      console.log('微信API请求数据:', {
        fileName: requestData.fileName,
        keyLength: requestData.keyLength,
        allowPrinting: requestData.allowPrinting,
        allowCopy: requestData.allowCopy,
        allowModify: requestData.allowModify,
        allowAnnotations: requestData.allowAnnotations,
        allowFormFilling: requestData.allowFormFilling
      });

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/add-password'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            console.log('微信API响应:', res);

            if (res.statusCode === 200 && res.data.success) {
              // 成功响应
              resolve({
                fileData: res.data.fileData, // base64格式
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              // 错误响应
              const errorMsg = res.data?.message || '密码保护添加失败';
              console.error('微信API错误:', errorMsg);
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            console.error('微信API请求失败:', error);
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      console.error('文件处理失败:', error);
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // 移除密码保护 - 微信小程序专用版本
  async removePassword(filePath, options = {}, onProgress = null) {
    console.log('使用微信小程序专用API移除密码保护...');
    console.log('文件路径:', filePath);
    console.log('选项:', options);

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      console.log('文件转换为base64成功，长度:', base64Data.length);

      // 2. 构建请求数据，匹配后端WechatRemovePasswordRequest模型
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'upload.pdf',
        password: options.password || ''
      };

      console.log('微信API请求数据:', {
        fileName: requestData.fileName
      });

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/remove-password'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            console.log('微信API响应:', res);

            if (res.statusCode === 200 && res.data.success) {
              // 成功响应
              resolve({
                fileData: res.data.fileData, // base64格式
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              // 错误响应
              const errorMsg = res.data?.message || '密码保护移除失败';
              console.error('微信API错误:', errorMsg);
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            console.error('微信API请求失败:', error);
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      console.error('文件处理失败:', error);
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // 添加水印 - 微信小程序专用版本
  async addWatermark(filePath, options = {}, onProgress = null) {
    // 验证必需参数
    if (!options.watermarkText || options.watermarkText.trim() === '') {
      throw new Error('水印文字不能为空');
    }

    console.log('使用微信小程序专用API添加水印...');
    console.log('文件路径:', filePath);

    try {
      // 1. 读取文件并转换为base64
      const fs = wx.getFileSystemManager();
      const fileBuffer = fs.readFileSync(filePath);
      const base64Data = wx.arrayBufferToBase64(fileBuffer);

      console.log('文件转换为base64成功，长度:', base64Data.length);

      // 2. 构建请求数据
      const requestData = {
        fileData: base64Data,
        fileName: options.fileName || 'upload.pdf',
        watermarkType: options.watermarkType || 'text',
        watermarkText: options.watermarkText,
        watermarkImageData: options.watermarkImageData || null,
        alphabet: options.alphabet || 'roman',
        fontSize: parseFloat(options.fontSize || 30),
        rotation: parseFloat(options.rotation || 0),
        opacity: parseFloat(options.opacity || 0.5),
        widthSpacer: parseInt(options.widthSpacer || 50),
        heightSpacer: parseInt(options.heightSpacer || 50),
        customColor: options.customColor || '#d3d3d3',
        convertPDFToImage: Boolean(options.convertPDFToImage)
      };

      console.log('微信API请求数据:', {
        fileName: requestData.fileName,
        watermarkType: requestData.watermarkType,
        watermarkText: requestData.watermarkText,
        fontSize: requestData.fontSize,
        opacity: requestData.opacity
      });

      // 3. 调用微信专用API
      return new Promise((resolve, reject) => {
        wx.request({
          url: getApiUrl('/miniprogram/wechat/add-watermark'),
          method: 'POST',
          data: requestData,
          header: {
            'content-type': 'application/json',
            ...(apiClient.apiKey && { 'X-API-KEY': apiClient.apiKey })
          },
          success: (res) => {
            console.log('微信API响应:', res);

            if (res.statusCode === 200 && res.data.success) {
              // 成功响应
              resolve({
                fileData: res.data.fileData, // base64格式
                fileName: res.data.fileName,
                fileSize: res.data.fileSize,
                processingTime: res.data.processingTime,
                isDownloaded: true,
                tempFilePath: `temp_${Date.now()}.pdf`
              });
            } else {
              // 错误响应
              const errorMsg = res.data?.message || '水印添加失败';
              console.error('微信API错误:', errorMsg);
              reject(new Error(errorMsg));
            }
          },
          fail: (error) => {
            console.error('微信API请求失败:', error);
            reject(new Error('网络请求失败: ' + error.errMsg));
          }
        });
      });

    } catch (error) {
      console.error('文件处理失败:', error);
      throw new Error('文件处理失败: ' + error.message);
    }
  },

  // 移除水印 (后端暂不支持)
  async removeWatermark(filePath, watermarkText = '', onProgress = null) {
    throw new Error('后端暂不支持移除水印功能');
  },



  // 修复PDF文件
  async repairPDF(filePath, onProgress = null) {
    console.log('调用PDF修复API:', filePath);

    const formData = {
      // PDF修复不需要额外参数
    };

    return apiClient.uploadFile('/general/repair-document', filePath, formData, onProgress);
  },

  // 获取PDF信息
  async getPDFInfo(filePath, onProgress = null) {
    return apiClient.uploadFile('/security/get-info-on-pdf', filePath, {}, onProgress);
  }
};

/**
 * PDF分析服务
 */
const AnalysisService = {
  // 获取PDF页数
  async getPageCount(filePath, onProgress = null) {
    return apiClient.uploadFile('/analysis/page-count', filePath, {}, onProgress);
  },

  // 获取PDF基本信息
  async getBasicInfo(filePath, onProgress = null) {
    return apiClient.uploadFile('/analysis/basic-info', filePath, {}, onProgress);
  },

  // 获取页面尺寸
  async getPageDimensions(filePath, onProgress = null) {
    return apiClient.uploadFile('/analysis/page-dimensions', filePath, {}, onProgress);
  },

  // 获取安全信息
  async getSecurityInfo(filePath, onProgress = null) {
    return apiClient.uploadFile('/analysis/security-info', filePath, {}, onProgress);
  },

  // 获取完整PDF信息（组合调用）
  async getFullInfo(filePath, onProgress = null) {
    try {
      const [pageCount, basicInfo, dimensions, security] = await Promise.all([
        this.getPageCount(filePath, onProgress),
        this.getBasicInfo(filePath, onProgress),
        this.getPageDimensions(filePath, onProgress),
        this.getSecurityInfo(filePath, onProgress)
      ]);

      return {
        pageCount,
        basicInfo,
        dimensions,
        security
      };
    } catch (error) {
      throw new Error(`获取PDF信息失败: ${error.message}`);
    }
  }
};

/**
 * PDF其他功能服务
 */
const MiscService = {
  // 压缩PDF
  async compressPDF(filePath, optimizeLevel = 1, onProgress = null) {
    const formData = { optimizeLevel };
    return apiClient.uploadFile('/misc/compress-pdf', filePath, formData, onProgress);
  },

  // OCR识别
  async ocrPDF(filePath, options = {}, onProgress = null) {
    const formData = {
      languages: options.languages || 'eng+chi_sim',
      sidecar: options.sidecar || false,
      deskew: options.deskew || false,
      clean: options.clean || false,
      cleanFinal: options.cleanFinal || false,
      ocrType: options.ocrType || 'skip-text'
    };
    return apiClient.uploadFile('/misc/ocr-pdf', filePath, formData, onProgress);
  },

  // 比较PDF
  async comparePDF(filePath1, filePath2, options = {}, onProgress = null) {
    const formData = {
      highlightColor1: options.highlightColor1 || '#ff0000',
      highlightColor2: options.highlightColor2 || '#008000'
    };

    // 由于需要上传两个文件，我们需要特殊处理
    return new Promise((resolve, reject) => {
      const uploadTask = wx.uploadFile({
        url: `${apiClient.baseURL}/misc/compare`,
        filePath: filePath1,
        name: 'fileInput1',
        formData: {
          ...formData,
          // 第二个文件需要通过其他方式处理，这里先简化
          fileInput2Path: filePath2
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data.data);
            } else {
              reject(new Error(data.message || '比较失败'));
            }
          } catch (error) {
            reject(new Error('响应解析失败'));
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '网络请求失败'));
        }
      });

      if (onProgress) {
        uploadTask.onProgressUpdate(onProgress);
      }
    });
  }
};

module.exports = {
  APIClient,
  apiClient,
  ConvertService,
  PDFService,
  SecurityService,
  AnalysisService,
  MiscService
};
