# Labels names are important as they are used by Release Drafter to decide
# regarding where to record them in changelog or if to skip them.
#
# The repository labels will be automatically configured using this file and
# the GitHub Action https://github.com/marketplace/actions/github-labeler.
- name: "Licenses"
  color: "EDEDED"
  from_name: "licenses"
- name: "Back End"
  color: "20CE6C"
  description: "Issues or pull requests related to back-end development"
  from_name: "Back end"
- name: "Bug"
  description: "Something isn't working"
  color: "EB9CA6"
  from_name: "bug"
- name: "dependencies"
  description: "Pull requests that update a dependency file"
  color: "5AA8FC"
- name: "Docker"
  description: "Pull requests that update Docker code"
  color: "1FCEFF"
  from_name: "docker"
- name: "Documentation"
  description: "Improvements or additions to documentation"
  color: "35ABFF"
  from_name: "documentation"
- name: "Done for next release"
  color: "0CDBD1"
  description: "Items that are completed and will be included in the next release"
- name: "Done"
  color: "60F13B"
- name: "duplicate"
  description: "This issue or pull request already exists"
  color: "CDD1D5"
- name: "enhancement"
  description: "New feature or request"
  color: "A0EEEE"
- name: "fix needs confirmation"
  color: "60A1E7"
  description: "Fix needs to be confirmed"
- name: "Front End"
  color: "BBD2F1"
  description: "Issues or pull requests related to front-end development"
- name: "github-actions"
  description: "Pull requests that update GitHub Actions code"
  color: "999999"
  from_name: "github_actions"
- name: "good first issue"
  description: "Good for newcomers"
  color: "C1B8FF"
- name: "help wanted"
  description: "Extra attention is needed"
  color: "00E6C4"
- name: "invalid"
  description: "This doesn't seem right"
  color: "E5E566"
- name: "Java"
  description: "Pull requests that update Java code"
  color: "FF9E1F"
  from_name: "java"
- name: "Long-term Enhancement"
  color: "BFDEC3"
  description: "Enhancements planned for the long term"
- name: "more-info-needed"
  color: "00E4F8"
  description: "More information is needed"
- name: "needs investigation"
  color: "B8C3A7"
  description: "Issues that require further investigation"
- name: "Prioritised enhancement"
  color: "4BA2EE"
  description: "High-priority enhancements"
- name: "question"
  description: "Further information is requested"
  color: "D97EE5"
- name: "Translation"
  color: "9FABF9"
  from_name: "translation"
- name: "upstream"
  color: "DEDEDE"
- name: "v2"
  color: "FFFF00"
- name: "wontfix"
  description: "This will not be worked on"
  color: "FFFFFF"
- name: "Security"
  color: "000000"
  description: "Security-related issues or pull requests"
- name: "API"
  color: "FFFF00"
  description: "API-related issues or pull requests"
- name: "Test"
  color: "FF9E1F"
  description: "Testing-related issues or pull requests"
- name: "Stale"
  color: "000000"
  description: "Issues or pull requests that have become inactive"
- name: "Priority: Critical"
  color: "000000"
  description: "Issues or pull requests with the highest priority"
- name: "Priority: High"
  color: "FF0000"
  description: "Issues or pull requests with high priority"
- name: "Priority: Medium"
  color: "FFFF00"
  description: "Issues or pull requests with medium priority"
- name: "Priority: Low"
  color: "00FF00"
  description: "Issues or pull requests with low priority"
- name: "Devtools"
  color: "FF9E1F"
  description: "Development tools"
- name: "Bugfix"
  color: "FF9E1F"
  description: "Pull requests that fix bugs"
- name: "Gradle"
  color: "FF9E1F"
  description: "Pull requests that update Gradle code"
- name: "build"
  color: "1E90FF"
  description: "Changes that affect the build system or external dependencies"
- name: "chore"
  color: "FFD700"
  description: "Routine tasks or maintenance that don't modify src or test files"
- name: "ci"
  color: "4682B4"
  description: "Changes to CI configuration files and scripts"
- name: "perf"
  color: "FF69B4"
  description: "Changes that improve performance"
- name: "refactor"
  color: "9932CC"
  description: "Code changes that neither fix a bug nor add a feature"
- name: "revert"
  color: "DC143C"
  description: "Reverts a previous commit"
- name: "style"
  color: "FFA500"
  description: "Changes that do not affect the meaning of the code (formatting, etc.)"
- name: "admin"
  color: "195055"
- name: "codex"
  color: "ededed"
  description: null
- name: "Github"
  color: "0052CC"
- name: "github_actions"
  color: "000000"
  description: "Pull requests that update GitHub Actions code"
- name: "needs-changes"
  color: "A65A86"
- name: "on-hold"
  color: "2526F9"
- name: "python"
  color: "2b67c6"
  description: "Pull requests that update Python code"
- name: "size:L"
  color: "eb9500"
  description: "This PR changes 100-499 lines ignoring generated files."
- name: "size:M"
  color: "ebb800"
  description: "This PR changes 30-99 lines ignoring generated files."
- name: "size:S"
  color: "77b800"
  description: "This PR changes 10-29 lines ignoring generated files."
- name: "size:XL"
  color: "ff823f"
  description: "This PR changes 500-999 lines ignoring generated files."
- name: "size:XS"
  color: "00ff00"
  description: "This PR changes 0-9 lines ignoring generated files."
- name: "size:XXL"
  color: "ffb8b8"
  description: "This PR changes 1000+ lines ignoring generated files."
- name: "to research"
  color: "FBCA04"
