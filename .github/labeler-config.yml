Translation:
  - changed-files:
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/messages_*_*.properties'
    - any-glob-to-any-file: 'scripts/ignore_translation.toml'
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/templates/fragments/languages.html'

Front End:
  - changed-files:
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/templates/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/static/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/web/**'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/UI/**/*'

Java:
  - changed-files:
    - any-glob-to-any-file: 'common/src/main/java/**/*.java'
    - any-glob-to-any-file: 'proprietary/src/main/java/**/*.java'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/**/*.java'

Back End:
  - changed-files:
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/config/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/settings.yml.template'
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/application.properties'
    - any-glob-to-any-file: 'stirling-pdf/src/main/resources/banner.txt'
    - any-glob-to-any-file: 'scripts/png_to_webp.py'
    - any-glob-to-any-file: 'split_photos.py'

Security:
  - changed-files:
    - any-glob-to-any-file: 'proprietary/src/main/java/stirling/software/proprietary/security/**/*'
    - any-glob-to-any-file: 'scripts/download-security-jar.sh'
    - any-glob-to-any-file: '.github/workflows/dependency-review.yml'
    - any-glob-to-any-file: '.github/workflows/scorecards.yml'

API:
  - changed-files:
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/config/OpenApiConfig.java'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/web/MetricsController.java'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/api/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/main/java/stirling/software/SPDF/model/api/**/*'
    - any-glob-to-any-file: 'scripts/png_to_webp.py'
    - any-glob-to-any-file: 'split_photos.py'
    - any-glob-to-any-file: '.github/workflows/swagger.yml'

Documentation:
  - changed-files:
    - any-glob-to-any-file: '**/*.md'
    - any-glob-to-any-file: 'scripts/counter_translation.py'
    - any-glob-to-any-file: 'scripts/ignore_translation.toml'

Docker:
  - changed-files:
    - any-glob-to-any-file: '.github/workflows/build.yml'
    - any-glob-to-any-file: '.github/workflows/push-docker.yml'
    - any-glob-to-any-file: 'Dockerfile'
    - any-glob-to-any-file: 'Dockerfile.fat'
    - any-glob-to-any-file: 'Dockerfile.ultra-lite'
    - any-glob-to-any-file: 'exampleYmlFiles/*.yml'
    - any-glob-to-any-file: 'scripts/download-security-jar.sh'
    - any-glob-to-any-file: 'scripts/init.sh'
    - any-glob-to-any-file: 'scripts/init-without-ocr.sh'
    - any-glob-to-any-file: 'scripts/installFonts.sh'
    - any-glob-to-any-file: 'test.sh'
    - any-glob-to-any-file: 'test2.sh'

Devtools:
  - changed-files:
    - any-glob-to-any-file: '.devcontainer/**/*'
    - any-glob-to-any-file: 'Dockerfile.dev'

Test:
  - changed-files:
    - any-glob-to-any-file: 'cucumber/**/*'
    - any-glob-to-any-file: 'common/src/test/**/*'
    - any-glob-to-any-file: 'proprietary/src/test/**/*'
    - any-glob-to-any-file: 'stirling-pdf/src/test/**/*'
    - any-glob-to-any-file: 'src/testing/**/*'
    - any-glob-to-any-file: '.pre-commit-config'
    - any-glob-to-any-file: '.github/workflows/pre_commit.yml'
    - any-glob-to-any-file: '.github/workflows/scorecards.yml'

Github:
  - changed-files:
    - any-glob-to-any-file: '.github/**/*'
