# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "gradle" # See documentation for possible values
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    rebase-strategy: "auto"

  - package-ecosystem: "docker"
    directory: "/" # Location of Dockerfile
    schedule:
      interval: "weekly"

  - package-ecosystem: github-actions
    directory: /
    schedule:
      interval: weekly
