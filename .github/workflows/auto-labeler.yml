name: "Pull Request Labeler"
on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  labeler:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Apply Labels
        uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          configuration-path: .github/labeler-config.yml
          sync-labels: true
