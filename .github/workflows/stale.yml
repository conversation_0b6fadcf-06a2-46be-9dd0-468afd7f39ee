name: Close stale issues

on:
  schedule:
    - cron: "30 0 * * *"
  workflow_dispatch:

permissions:
  contents: read

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: 30 days stale issues
        uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9.1.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          days-before-stale: 30
          days-before-close: 7
          stale-issue-message: >
            This issue has been automatically marked as stale because it has had no recent activity.
            It will be closed if no further activity occurs. Thank you for your contributions.
          close-issue-message: >
            This issue has been automatically closed because it has had no recent activity after being marked as stale.
            Please reopen if you need further assistance.
          stale-issue-label: "Stale"
          remove-stale-when-updated: true
          only-issue-labels: "more-info-needed"
          days-before-pr-stale: -1 # Prevents PRs from being marked as stale
          days-before-pr-close: -1 # Prevents PRs from being closed
          start-date: "2024-07-06T00:00:00Z" # ISO 8601 Format
