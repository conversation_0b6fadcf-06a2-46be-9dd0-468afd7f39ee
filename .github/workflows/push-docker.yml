name: Push Docker Image with VersionNumber

on:
  workflow_dispatch:
  push:
    branches:
      - master
      - main

permissions:
  contents: read

jobs:
  push:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      id-token: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 17
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "temurin"

      - uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        with:
          gradle-version: 8.14

      - name: Run Gradle Command
        run: ./gradlew clean build
        env:
          DISABLE_ADDITIONAL_FEATURES: true
          STIRLING_PDF_DESKTOP_UI: false

      - name: Install cosign
        if: github.ref == 'refs/heads/master'
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1
        with:
          cosign-release: "v2.4.1"

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Get version number
        id: versionNumber
        run: echo "versionNumber=$(./gradlew printVersion --quiet | tail -1)" >> $GITHUB_OUTPUT

      - name: Login to Docker Hub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_API }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

      - name: Convert repository owner to lowercase
        id: repoowner
        run: echo "lowercase=$(echo ${{ github.repository_owner }} | awk '{print tolower($0)}')" >> $GITHUB_OUTPUT

      - name: Generate tags
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        if: github.ref != 'refs/heads/main'
        with:
          images: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/stirling-pdf
            ${{ secrets.DOCKER_HUB_ORG_USERNAME }}/stirling-pdf
          tags: |
            type=raw,value=${{ steps.versionNumber.outputs.versionNumber }},enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/master' }}

      - name: Build and push main Dockerfile
        id: build-push-regular
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        if: github.ref != 'refs/heads/main'
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: ./Dockerfile
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: VERSION_TAG=${{ steps.versionNumber.outputs.versionNumber }}
          platforms: linux/amd64,linux/arm64/v8
          provenance: true
          sbom: true

      - name: Sign regular images
        if: github.ref == 'refs/heads/master'
        env:
          DIGEST: ${{ steps.build-push-regular.outputs.digest }}
          TAGS: ${{ steps.meta.outputs.tags }}
          COSIGN_PRIVATE_KEY: ${{ secrets.COSIGN_PRIVATE_KEY }}
          COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}
        run: |
          echo "$TAGS" | tr ',' '\n' | while read -r tag; do
            cosign sign --yes \
              --key env://COSIGN_PRIVATE_KEY \
              "${tag}@${DIGEST}"
          done

      - name: Generate tags ultra-lite
        id: meta2
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        if: github.ref != 'refs/heads/main'
        with:
          images: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/stirling-pdf
            ${{ secrets.DOCKER_HUB_ORG_USERNAME }}/stirling-pdf
          tags: |
            type=raw,value=${{ steps.versionNumber.outputs.versionNumber }}-ultra-lite,enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=latest-ultra-lite,enable=${{ github.ref == 'refs/heads/master' }}

      - name: Build and push Dockerfile-ultra-lite
        id: build-push-lite
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        if: github.ref != 'refs/heads/main'
        with:
          context: .
          file: ./Dockerfile.ultra-lite
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ steps.meta2.outputs.tags }}
          labels: ${{ steps.meta2.outputs.labels }}
          build-args: VERSION_TAG=${{ steps.versionNumber.outputs.versionNumber }}
          platforms: linux/amd64,linux/arm64/v8
          provenance: true
          sbom: true

      - name: Generate tags fat
        id: meta3
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          images: |
            ${{ secrets.DOCKER_HUB_USERNAME }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/s-pdf
            ghcr.io/${{ steps.repoowner.outputs.lowercase }}/stirling-pdf
            ${{ secrets.DOCKER_HUB_ORG_USERNAME }}/stirling-pdf
          tags: |
            type=raw,value=${{ steps.versionNumber.outputs.versionNumber }}-fat,enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=latest-fat,enable=${{ github.ref == 'refs/heads/master' }}
            type=raw,value=alpha,enable=${{ github.ref == 'refs/heads/main' }}

      - name: Build and push main Dockerfile fat
        id: build-push-fat
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: ./Dockerfile.fat
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ steps.meta3.outputs.tags }}
          labels: ${{ steps.meta3.outputs.labels }}
          build-args: VERSION_TAG=${{ steps.versionNumber.outputs.versionNumber }}
          platforms: linux/amd64,linux/arm64/v8
          provenance: true
          sbom: true

      - name: Sign fat images
        if: github.ref == 'refs/heads/master'
        env:
          DIGEST: ${{ steps.build-push-fat.outputs.digest }}
          TAGS: ${{ steps.meta3.outputs.tags }}
          COSIGN_PRIVATE_KEY: ${{ secrets.COSIGN_PRIVATE_KEY }}
          COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}
        run: |
          echo "$TAGS" | tr ',' '\n' | while read -r tag; do
            cosign sign --key env://COSIGN_PRIVATE_KEY --yes "${tag}@${DIGEST}"
          done
