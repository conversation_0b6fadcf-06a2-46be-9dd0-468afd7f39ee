name: License Report Workflow

on:
  push:
    branches:
      - main
    paths:
      - "build.gradle"

permissions:
  contents: read

jobs:
  generate-license-report:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      repository-projects: write # Required for enabling automerge
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Check out code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Setup GitHub App Bot
        id: setup-bot
        uses: ./.github/actions/setup-bot
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - name: Set up JDK 17
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "adopt"

      - name: <PERSON>up Gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Check licenses for compatibility
        run: ./gradlew clean checkLicense

      - name: Upload artifact on failure
        if: failure()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dependencies-without-allowed-license.json
          path: build/reports/dependency-license/dependencies-without-allowed-license.json
          retention-days: 3

      - name: Move and rename license file
        run: |
          mv build/reports/dependency-license/index.json stirling-pdf/src/main/resources/static/3rdPartyLicenses.json

      - name: Commit changes
        run: |
          git add stirling-pdf/src/main/resources/static/3rdPartyLicenses.json
          git diff --staged --quiet || echo "CHANGES_DETECTED=true" >> $GITHUB_ENV

      - name: Create Pull Request
        id: cpr
        if: env.CHANGES_DETECTED == 'true'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ steps.setup-bot.outputs.token }}
          commit-message: "Update 3rd Party Licenses"
          committer: ${{ steps.setup-bot.outputs.committer }}
          author: ${{ steps.setup-bot.outputs.committer }}
          signoff: true
          branch: update-3rd-party-licenses
          title: "Update 3rd Party Licenses"
          body: |
            Auto-generated by ${{ steps.setup-bot.outputs.app-slug }}[bot]
          labels: Licenses,github-actions
          draft: false
          delete-branch: true
          sign-commits: true

      - name: Enable Pull Request Automerge
        if: steps.cpr.outputs.pull-request-operation == 'created'
        run: gh pr merge --squash --auto "${{ steps.cpr.outputs.pull-request-number }}"
        env:
          GH_TOKEN: ${{ steps.setup-bot.outputs.token }}
