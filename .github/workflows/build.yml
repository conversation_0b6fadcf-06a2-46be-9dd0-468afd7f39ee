name: Build repo

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    permissions:
      actions: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        jdk-version: [17, 21]
        spring-security: [true, false]

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK ${{ matrix.jdk-version }}
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: ${{ matrix.jdk-version }}
          distribution: "temurin"

      - name: Build with <PERSON>radle and spring security ${{ matrix.spring-security }}
        run: ./gradlew clean build
        env:
          DISABLE_ADDITIONAL_FEATURES: ${{ matrix.spring-security }}

      - name: Check Test Reports Exist
        id: check-reports
        if: always()
        run: |
          declare -a dirs=(
            "stirling-pdf/build/reports/tests/"
            "stirling-pdf/build/test-results/"
            "common/build/reports/tests/"
            "common/build/test-results/"
            "proprietary/build/reports/tests/"
            "proprietary/build/test-results/"
          )
          missing_reports=()
          for dir in "${dirs[@]}"; do
            if [ ! -d "$dir" ]; then
              missing_reports+=("$dir")
            fi
          done
          if [ ${#missing_reports[@]} -gt 0 ]; then
            echo "ERROR: The following required test report directories are missing:"
            printf '%s\n' "${missing_reports[@]}"
            exit 1
          fi
          echo "All required test report directories are present"

      - name: Upload Test Reports
        if: always()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: test-reports-jdk-${{ matrix.jdk-version }}-spring-security-${{ matrix.spring-security }}
          path: |
            stirling-pdf/build/reports/tests/
            stirling-pdf/build/test-results/
            stirling-pdf/build/reports/problems/
            common/build/reports/tests/
            common/build/test-results/
            common/build/reports/problems/
            proprietary/build/reports/tests/
            proprietary/build/test-results/
            proprietary/build/reports/problems/
            build/reports/problems/
          retention-days: 3
          if-no-files-found: warn

  check-licence:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 17
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "adopt"

      - name: check the licenses for compatibility
        run: ./gradlew clean checkLicense

      - name: FAILED - check the licenses for compatibility
        if: failure()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: dependencies-without-allowed-license.json
          path: |
            build/reports/dependency-license/dependencies-without-allowed-license.json
          retention-days: 3

  docker-compose-tests:
    # if: github.event_name == 'push' && github.ref == 'refs/heads/main' ||
    #     (github.event_name == 'pull_request' &&
    #     contains(github.event.pull_request.labels.*.name, 'licenses') == false &&
    #     (
    #       contains(github.event.pull_request.labels.*.name, 'Front End') ||
    #       contains(github.event.pull_request.labels.*.name, 'Java') ||
    #       contains(github.event.pull_request.labels.*.name, 'Back End') ||
    #       contains(github.event.pull_request.labels.*.name, 'Security') ||
    #       contains(github.event.pull_request.labels.*.name, 'API') ||
    #       contains(github.event.pull_request.labels.*.name, 'Docker') ||
    #       contains(github.event.pull_request.labels.*.name, 'Test')
    #     )
    #     )

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout Repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Java 17
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "adopt"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Install Docker Compose
        run: |
          sudo curl -SL "https://github.com/docker/compose/releases/download/v2.37.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
          sudo chmod +x /usr/local/bin/docker-compose

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.12"
          cache: 'pip' # caching pip dependencies

      - name: Pip requirements
        run: |
          pip install --require-hashes -r ./testing/cucumber/requirements.txt

      - name: Run Docker Compose Tests
        run: |
          chmod +x ./testing/test_webpages.sh
          chmod +x ./testing/test.sh
          chmod +x ./testing/test_disabledEndpoints.sh
          ./testing/test.sh
