name: Test Installers Build

on:
  workflow_dispatch:
    inputs:
      test_mode:
        description: "Run in test mode (skip release step)"
        required: false
        default: "false"
  release:
    types: [created]

permissions:
  contents: read

jobs:
  read_versions:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.versionNumber.outputs.versionNumber }}
      versionMac: ${{ steps.versionNumberMac.outputs.versionNumberMac }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # Get version number
      - name: Get version number
        id: versionNumber
        run: |
          VERSION=$(grep "^version =" build.gradle | awk -F'"' '{print $2}')
          echo "versionNumber=$VERSION" >> $GITHUB_OUTPUT

      - name: Get version number mac
        id: versionNumberMac
        run: |
          VERSION=$(grep "^version =" build.gradle | awk -F'"' '{print $2}')
          CURRENT_YEAR=$(date +'%Y')
          IFS='.' read -r -a VERSION_PARTS <<< "$VERSION"
          MAC_VERSION="$CURRENT_YEAR.${VERSION_PARTS[1]:-0}.${VERSION_PARTS[2]:-0}"
          echo "versionNumberMac=$MAC_VERSION" >> $GITHUB_OUTPUT

  build-portable:
    needs: read_versions
    runs-on: ubuntu-latest
    strategy:
      matrix:
        disable_security: [true, false]
        include:
          - disable_security: false
            file_suffix: "-with-login"
          - disable_security: true
            file_suffix: ""
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "21"
          distribution: "temurin"

      - uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        with:
          gradle-version: 8.14

      - name: Generate jar (Disable Security=${{ matrix.disable_security }})
        run: ./gradlew clean createExe
        env:
          DISABLE_ADDITIONAL_FEATURES: ${{ matrix.disable_security }}
          STIRLING_PDF_DESKTOP_UI: false

      - name: Rename binaries
        run: |
          mkdir ./binaries
          mv ./build/launch4j/Stirling-PDF.exe ./binaries/win-Stirling-PDF-portable-Server${{ matrix.file_suffix }}.exe
          mv ./build/libs/Stirling-PDF-${{ needs.read_versions.outputs.version }}.jar ./binaries/Stirling-PDF${{ matrix.file_suffix }}.jar

      - name: Upload build artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          retention-days: 1
          if-no-files-found: error
          name: stirling${{ matrix.file_suffix }}-binaries
          path: |
            ./binaries/*

  sign_verify-portable:
    needs: [build-portable, read_versions]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        disable_security: [true, false]
        include:
          - disable_security: false
            file_suffix: "with-login-"
          - disable_security: true
            file_suffix: ""
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Download build artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: stirling-${{ matrix.file_suffix }}binaries

      - name: Display structure of downloaded files
        run: ls -R

      - name: Upload signed artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          retention-days: 1
          if-no-files-found: error
          name: stirling-${{ matrix.file_suffix }}signed
          path: |
            ./*
            !cosign.*

  build-installers:
    needs: read_versions
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: win-
          - os: macos-latest
            platform: mac-
          # - os: ubuntu-latest
          #   platform: linux-
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 21
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "21"
          distribution: "temurin"

      - uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        with:
          gradle-version: 8.14

      # Install Windows dependencies
      - name: Install WiX Toolset
        if: matrix.os == 'windows-latest'
        run: |
          curl -L -o wix.exe https://github.com/wixtoolset/wix3/releases/download/wix3141rtm/wix314.exe
          .\wix.exe /install /quiet

      # Build installer
      - name: Build Installer
        run: ./gradlew build jpackage -x test --info
        env:
          DISABLE_ADDITIONAL_FEATURES: true
          STIRLING_PDF_DESKTOP_UI: true
          BROWSER_OPEN: true

      - name: Set up JDK (x86_64)
        if: matrix.os == 'macos-latest'
        run: |
          curl -L -o jdk.tar.gz https://cdn.azul.com/zulu/bin/zulu17.56.15-ca-jdk17.0.14-macosx_x64.tar.gz
          mkdir -p zulu17
          tar -xzf jdk.tar.gz -C zulu17 --strip-components=1
          echo "JAVA_HOME=$PWD/zulu17" >> $GITHUB_ENV
          echo "$PWD/zulu17/bin" >> $GITHUB_PATH

      -   name: Verify JDK architecture
          if: matrix.os == 'macos-latest'
          run: file $JAVA_HOME/bin/java

      -   name: Build project and run jpackage (x86_64)
          if: matrix.os == 'macos-latest'
          run: arch -x86_64 ./gradlew jpackageMacX64

      # Rename and collect artifacts based on OS
      - name: Prepare artifacts
        id: prepare
        shell: bash
        run: |
          ls -lah ./build/jpackage/
          mkdir ./binaries
          if [ "${{ matrix.os }}" = "windows-latest" ]; then
            mv "./build/jpackage/Stirling PDF-${{ needs.read_versions.outputs.version }}.exe" "./binaries/Stirling-PDF-win-installer.exe"
          elif [ "${{ matrix.os }}" = "macos-latest" ]; then
            mv "./build/jpackage/Stirling PDF-${{ needs.read_versions.outputs.versionMac }}.dmg" "./binaries/Stirling-PDF-mac-installer.dmg"
            mv "./build/jpackage/x86_64/Stirling PDF (x86_64)-${{ needs.read_versions.outputs.versionMac }}.dmg" "./binaries/Stirling-PDF-mac-x86_64-installer.dmg"
          else
            mv "./build/jpackage/stirling-pdf_${{ needs.read_versions.outputs.version }}-1_amd64.deb" "./binaries/Stirling-PDF-linux-installer.deb"
          fi

      - name: Display structure of downloaded files
        run: ls -R ./binaries

      - name: Upload build artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          retention-days: 1
          if-no-files-found: error
          name: ${{ matrix.platform }}binaries
          path: |
            ./binaries/*

  sign_verify:
    needs: [read_versions, build-installers]
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: win-
          - os: macos-latest
            platform: mac-
          # - os: ubuntu-latest
          #   platform: linux-
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Download build artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: ${{ matrix.platform }}binaries

      - name: Display structure of downloaded files
        run: ls -R

      - name: Install Cosign
        if: matrix.os == 'windows-latest'
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1

      - name: Generate key pair
        if: matrix.os == 'windows-latest'
        run: cosign generate-key-pair

      - name: Sign and generate attestations
        if: matrix.os == 'windows-latest'
        run: |
          cosign sign-blob \
            --key ./cosign.key \
            --yes \
            --output-signature ./Stirling-PDF-win-installer.exe.sig \
            ./Stirling-PDF-win-installer.exe

          cosign attest-blob \
            --predicate - \
            --key ./cosign.key \
            --yes \
            --output-attestation ./Stirling-PDF-win-installer.exe.intoto.jsonl \
            ./Stirling-PDF-win-installer.exe

          cosign verify-blob \
            --key ./cosign.pub \
            --signature ./Stirling-PDF-win-installer.exe.sig \
            ./Stirling-PDF-win-installer.exe

      - name: Display structure of downloaded files
        run: ls -R

      - name: Upload signed artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          retention-days: 1
          if-no-files-found: error
          name: ${{ matrix.platform }}signed
          path: |
            ./Stirling-PDF-${{ matrix.platform }}installer.*
            ./Stirling-PDF-${{ matrix.platform }}x86_64-installer.*
            !cosign.*

  create-release:
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.test_mode != 'true'
    needs: [read_versions, sign_verify, sign_verify-portable]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Download signed artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
      - name: Display structure of downloaded files
        run: ls -R
      - name: Upload binaries, attestations and signatures to Release and create GitHub Release
        uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8 # v2.3.2
        with:
          tag_name: v${{ needs.read_versions.outputs.version }}
          generate_release_notes: true
          files: |
            ./*signed/*
