name: UI test with <PERSON><PERSON>riverA<PERSON>

on:
  push:
    branches: ["master", "UITest", "testdriver"]

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Build with Gradle
        run: ./gradlew clean build
        env:
          DISABLE_ADDITIONAL_FEATURES: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Get version number
        id: versionNumber
        run: |
          VERSION=$(grep "^version =" build.gradle | awk -F'"' '{print $2}')
          echo "versionNumber=$VERSION" >> $GITHUB_OUTPUT

      - name: Login to Docker Hub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_API }}

      - name: Build and push test image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/test:test-${{ github.sha }}
          build-args: VERSION_TAG=${{ steps.versionNumber.outputs.versionNumber }}
          platforms: linux/amd64

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.VPS_SSH_KEY }}" > ../private.key
          sudo chmod 600 ../private.key

      - name: Deploy to VPS
        run: |
          cat > docker-compose.yml << EOF
          version: '3.3'
          services:
            stirling-pdf:
              container_name: stirling-pdf-test-${{ github.sha }}
              image: ${{ secrets.DOCKER_HUB_USERNAME }}/test:test-${{ github.sha }}
              ports:
                - "1337:8080"
              volumes:
                - /stirling/test-${{ github.sha }}/data:/usr/share/tessdata:rw
                - /stirling/test-${{ github.sha }}/config:/configs:rw
                - /stirling/test-${{ github.sha }}/logs:/logs:rw
              environment:
                DISABLE_ADDITIONAL_FEATURES: "true"
                SECURITY_ENABLELOGIN: "false"
                SYSTEM_DEFAULTLOCALE: en-GB
                UI_APPNAME: "Stirling-PDF Test"
                UI_HOMEDESCRIPTION: "Test Deployment"
                UI_APPNAMENAVBAR: "Test"
                SYSTEM_MAXFILESIZE: "100"
                METRICS_ENABLED: "true"
                SYSTEM_GOOGLEVISIBILITY: "false"
                SYSTEM_ENABLEANALYTICS: "false"
              restart: on-failure:5
          EOF

          scp -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null docker-compose.yml ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }}:/tmp/docker-compose.yml

          ssh -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }} << EOF
            mkdir -p /stirling/test-${{ github.sha }}/{data,config,logs}
            mv /tmp/docker-compose.yml /stirling/test-${{ github.sha }}/docker-compose.yml
            cd /stirling/test-${{ github.sha }}
            docker-compose pull
            docker-compose up -d
          EOF

  test:
    needs: deploy
    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Run TestDriver.ai
        uses: testdriverai/action@f0d0f45fdd684db628baa843fe9313f3ca3a8aa8 #1.1.3
        with:
          key: ${{secrets.TESTDRIVER_API_KEY}}
          prerun: |
            npm install
            npm run build
            npm install dashcam-chrome --save
            Start-Process "C:/Program Files/Google/Chrome/Application/chrome.exe" -ArgumentList "--start-maximized", "--load-extension=$(pwd)/node_modules/dashcam-chrome/build", "http://${{ secrets.VPS_HOST }}:1337"
            Start-Sleep -Seconds 20
          prompt: |
            1. /run testing/testdriver/test.yml
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FORCE_COLOR: "3"

  cleanup:
    needs: [deploy, test]
    runs-on: ubuntu-latest
    if: always()

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.VPS_SSH_KEY }}" > ../private.key
          sudo chmod 600 ../private.key

      - name: Cleanup deployment
        run: |
          ssh -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }} << EOF
            cd /stirling/test-${{ github.sha }}
            docker-compose down
            cd /stirling
            rm -rf test-${{ github.sha }}
          EOF
