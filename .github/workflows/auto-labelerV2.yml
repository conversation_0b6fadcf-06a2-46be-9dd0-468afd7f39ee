name: "Auto Pull Request Labeler V2"
on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  labeler:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup GitHub App Bot
        id: setup-bot
        uses: ./.github/actions/setup-bot
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - uses: srvaroa/labeler@0a20eccb8c94a1ee0bed5f16859aece1c45c3e55 # v1.13.0
        with:
          config_path: .github/labeler-config-srvaroa.yml
          use_local_config: false
          fail_on_error: true
        env:
          GITHUB_TOKEN: "${{ steps.setup-bot.outputs.token }}"
