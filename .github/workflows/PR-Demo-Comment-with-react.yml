name: PR Deployment via Comment

on:
  issue_comment:
    types: [created]

permissions:
  contents: read
  issues: write      # Required for adding reactions to comments
  pull-requests: read # Required for reading PR information

jobs:
  check-comment:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: read
    if: |
      github.event.issue.pull_request &&
      (
      contains(github.event.comment.body, 'prdeploy') ||
      contains(github.event.comment.body, 'deploypr')
      )
      &&
      (
        github.event.comment.user.login == 'frooodle' ||
        github.event.comment.user.login == 'sf298' ||
        github.event.comment.user.login == 'Ludy87' ||
        github.event.comment.user.login == 'LaserKaspar' ||
        github.event.comment.user.login == 'sbplat' ||
        github.event.comment.user.login == 'reecebrowne' ||
        github.event.comment.user.login == 'DarioGii' ||
        github.event.comment.user.login == '<PERSON>Yoh'
      )
    outputs:
      pr_number: ${{ steps.get-pr.outputs.pr_number }}
      pr_repository: ${{ steps.get-pr-info.outputs.repository }}
      pr_ref: ${{ steps.get-pr-info.outputs.ref }}
      comment_id: ${{ github.event.comment.id }}
      disable_security: ${{ steps.check-security-flag.outputs.disable_security }}
      enable_pro: ${{ steps.check-pro-flag.outputs.enable_pro }}
      enable_enterprise: ${{ steps.check-pro-flag.outputs.enable_enterprise }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      # Generate GitHub App token
      - name: Generate GitHub App Token
        id: generate-token
        uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - name: Get PR data
        id: get-pr
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            const prNumber = context.payload.issue.number;
            console.log(`PR Number: ${prNumber}`);
            core.setOutput('pr_number', prNumber);

      - name: Get PR repository and ref
        id: get-pr-info
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            const { owner, repo } = context.repo;
            const prNumber = context.payload.issue.number;

            const { data: pr } = await github.rest.pulls.get({
              owner,
              repo,
              pull_number: prNumber,
            });

            // For forks, use the full repository name, for internal PRs use the current repo
            const repository = pr.head.repo.fork ? pr.head.repo.full_name : `${owner}/${repo}`;

            console.log(`PR Repository: ${repository}`);
            console.log(`PR Branch: ${pr.head.ref}`);

            core.setOutput('repository', repository);
            core.setOutput('ref', pr.head.ref);

      - name: Check for security/login flag
        id: check-security-flag
        env:
          COMMENT_BODY: ${{ github.event.comment.body }}
        run: |
          if [[ "$COMMENT_BODY" == *"security"* ]] || [[ "$COMMENT_BODY" == *"login"* ]]; then
            echo "Security flags detected in comment"
            echo "disable_security=false" >> $GITHUB_OUTPUT
          else
            echo "No security flags detected in comment"
            echo "disable_security=true" >> $GITHUB_OUTPUT
          fi

      - name: Check for pro flag
        id: check-pro-flag
        env:
          COMMENT_BODY: ${{ github.event.comment.body }}
        run: |
          if [[ "$COMMENT_BODY" == *"pro"* ]] || [[ "$COMMENT_BODY" == *"premium"* ]]; then
            echo "pro flags detected in comment"
            echo "enable_pro=true" >> $GITHUB_OUTPUT
            echo "enable_enterprise=false" >> $GITHUB_OUTPUT
          elif [[ "$COMMENT_BODY" == *"enterprise"* ]]; then
            echo "enterprise flags detected in comment"
            echo "enable_enterprise=true" >> $GITHUB_OUTPUT
            echo "enable_pro=true" >> $GITHUB_OUTPUT
          else
            echo "No pro or enterprise flags detected in comment"
            echo "enable_pro=false" >> $GITHUB_OUTPUT
            echo "enable_enterprise=false" >> $GITHUB_OUTPUT
          fi

      - name: Add 'in_progress' reaction to comment
        id: add-eyes-reaction
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ steps.generate-token.outputs.token }}
          script: |
            console.log(`Adding eyes reaction to comment ID: ${context.payload.comment.id}`);
            try {
              const { data: reaction } = await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: context.payload.comment.id,
                content: 'eyes'
              });
              console.log(`Added reaction with ID: ${reaction.id}`);
              return { success: true, id: reaction.id };
            } catch (error) {
              console.error(`Failed to add reaction: ${error.message}`);
              console.error(error);
              return { success: false, error: error.message };
            }

  deploy-pr:
    needs: check-comment
    runs-on: ubuntu-latest
    permissions:
      contents: read
      issues: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Generate GitHub App Token
        id: generate-token
        uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - name: Checkout PR
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: ${{ needs.check-comment.outputs.pr_repository }}
          ref: ${{ needs.check-comment.outputs.pr_ref }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up JDK
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "temurin"

      - name: Run Gradle Command
        run: |
          if [ "${{ needs.check-comment.outputs.disable_security }}" == "true" ]; then
            export DISABLE_ADDITIONAL_FEATURES=true
          else
            export DISABLE_ADDITIONAL_FEATURES=false
          fi
          ./gradlew clean build
        env:
          STIRLING_PDF_DESKTOP_UI: false

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Get version number
        id: versionNumber
        run: |
          VERSION=$(grep "^version =" build.gradle | awk -F'"' '{print $2}')
          echo "versionNumber=$VERSION" >> $GITHUB_OUTPUT

      - name: Login to Docker Hub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_API }}

      - name: Build and push PR-specific image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/test:pr-${{ needs.check-comment.outputs.pr_number }}
          build-args: VERSION_TAG=alpha
          platforms: linux/amd64

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.VPS_SSH_KEY }}" > ../private.key
          sudo chmod 600 ../private.key

      - name: Deploy to VPS
        id: deploy
        run: |
          # Set security settings based on flags
          if [ "${{ needs.check-comment.outputs.disable_security }}" == "false" ]; then
            DISABLE_ADDITIONAL_FEATURES="false"
            LOGIN_SECURITY="true"
            SECURITY_STATUS="🔒 Security Enabled"
          else
            DISABLE_ADDITIONAL_FEATURES="true"
            LOGIN_SECURITY="false"
            SECURITY_STATUS="Security Disabled"
          fi

          # Set pro/enterprise settings (enterprise implies pro)
          if [ "${{ needs.check-comment.outputs.enable_enterprise }}" == "true" ]; then
            PREMIUM_ENABLED="true"
            PREMIUM_KEY="${{ secrets.ENTERPRISE_KEY }}"
            PREMIUM_PROFEATURES_AUDIT_ENABLED="true"
          elif [ "${{ needs.check-comment.outputs.enable_pro }}" == "true" ]; then
            PREMIUM_ENABLED="true"
            PREMIUM_KEY="${{ secrets.PREMIUM_KEY }}"
            PREMIUM_PROFEATURES_AUDIT_ENABLED="true"
          else
            PREMIUM_ENABLED="false"
            PREMIUM_KEY=""
            PREMIUM_PROFEATURES_AUDIT_ENABLED="false"
          fi

          # First create the docker-compose content locally
          cat > docker-compose.yml << EOF
          version: '3.3'
          services:
            stirling-pdf:
              container_name: stirling-pdf-pr-${{ needs.check-comment.outputs.pr_number }}
              image: ${{ secrets.DOCKER_HUB_USERNAME }}/test:pr-${{ needs.check-comment.outputs.pr_number }}
              ports:
                - "${{ needs.check-comment.outputs.pr_number }}:8080"
              volumes:
                - /stirling/PR-${{ needs.check-comment.outputs.pr_number }}/data:/usr/share/tessdata:rw
                - /stirling/PR-${{ needs.check-comment.outputs.pr_number }}/config:/configs:rw
                - /stirling/PR-${{ needs.check-comment.outputs.pr_number }}/logs:/logs:rw
              environment:
                DISABLE_ADDITIONAL_FEATURES: "${DISABLE_ADDITIONAL_FEATURES}"
                SECURITY_ENABLELOGIN: "${LOGIN_SECURITY}"
                SYSTEM_DEFAULTLOCALE: en-GB
                UI_APPNAME: "Stirling-PDF PR#${{ needs.check-comment.outputs.pr_number }}"
                UI_HOMEDESCRIPTION: "PR#${{ needs.check-comment.outputs.pr_number }} for Stirling-PDF Latest"
                UI_APPNAMENAVBAR: "PR#${{ needs.check-comment.outputs.pr_number }}"
                SYSTEM_MAXFILESIZE: "100"
                METRICS_ENABLED: "true"
                SYSTEM_GOOGLEVISIBILITY: "false"
                PREMIUM_KEY:                         "${PREMIUM_KEY}"
                PREMIUM_ENABLED:                     "${PREMIUM_ENABLED}"
                PREMIUM_PROFEATURES_AUDIT_ENABLED:   "${PREMIUM_PROFEATURES_AUDIT_ENABLED}"
              restart: on-failure:5
          EOF

          # Then copy the file and execute commands
          scp -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null docker-compose.yml ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }}:/tmp/docker-compose.yml

          ssh -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -T ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }} << ENDSSH
            # Create PR-specific directories
            mkdir -p /stirling/PR-${{ needs.check-comment.outputs.pr_number }}/{data,config,logs}

            # Move docker-compose file to correct location
            mv /tmp/docker-compose.yml /stirling/PR-${{ needs.check-comment.outputs.pr_number }}/docker-compose.yml

            # Start or restart the container
            cd /stirling/PR-${{ needs.check-comment.outputs.pr_number }}
            docker-compose pull
            docker-compose up -d
          ENDSSH

          # Set output for use in PR comment
          echo "security_status=${SECURITY_STATUS}" >> $GITHUB_ENV

      - name: Add success reaction to comment
        if: success()
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ steps.generate-token.outputs.token }}
          script: |
            console.log(`Adding rocket reaction to comment ID: ${{ needs.check-comment.outputs.comment_id }}`);
            try {
              const { data: reaction } = await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: ${{ needs.check-comment.outputs.comment_id }},
                content: 'rocket'
              });
              console.log(`Added rocket reaction with ID: ${reaction.id}`);
            } catch (error) {
              console.error(`Failed to add reaction: ${error.message}`);
              console.error(error);
            }

      - name: Add failure reaction to comment
        if: failure()
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ steps.generate-token.outputs.token }}
          script: |
            console.log(`Adding -1 reaction to comment ID: ${{ needs.check-comment.outputs.comment_id }}`);
            try {
              const { data: reaction } = await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: ${{ needs.check-comment.outputs.comment_id }},
                content: '-1'
              });
              console.log(`Added -1 reaction with ID: ${reaction.id}`);
            } catch (error) {
              console.error(`Failed to add reaction: ${error.message}`);
              console.error(error);
            }

      - name: Post deployment URL to PR
        if: success()
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ steps.generate-token.outputs.token }}
          script: |
            const { GITHUB_REPOSITORY } = process.env;
            const [repoOwner, repoName] = GITHUB_REPOSITORY.split('/');
            const prNumber = ${{ needs.check-comment.outputs.pr_number }};
            const securityStatus = process.env.security_status || "Security Disabled";

            const deploymentUrl = `http://${{ secrets.VPS_HOST }}:${prNumber}`;
            const commentBody = `## 🚀 PR Test Deployment\n\n` +
                              `Your PR has been deployed for testing!\n\n` +
                              `🔗 **Test URL:** [${deploymentUrl}](${deploymentUrl})\n` +
                              `${securityStatus}\n\n` +
                              `This deployment will be automatically cleaned up when the PR is closed.\n\n`;

            await github.rest.issues.createComment({
              owner: repoOwner,
              repo: repoName,
              issue_number: prNumber,
              body: commentBody
            });
