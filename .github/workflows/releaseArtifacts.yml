name: Release Artifacts

on:
  workflow_dispatch:
  release:
    types: [created]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        disable_security: [true, false]
        include:
          - disable_security: false
            file_suffix: "-with-login"
          - disable_security: true
            file_suffix: ""
    outputs:
      version: ${{ steps.versionNumber.outputs.versionNumber }}
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up JDK 17
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: "17"
          distribution: "temurin"

      - uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        with:
          gradle-version: 8.14

      - name: Generate jar (Disable Security=${{ matrix.disable_security }})
        run: ./gradlew clean createExe
        env:
          DISABLE_ADDITIONAL_FEATURES: ${{ matrix.disable_security }}
          STIRLING_PDF_DESKTOP_UI: false

      - name: Get version number
        id: versionNumber
        run: |
          VERSION=$(grep "^version =" build.gradle | awk -F'"' '{print $2}')
          echo "versionNumber=$VERSION" >> $GITHUB_OUTPUT

      - name: Rename binaries
        run: |
          mv ./build/launch4j/Stirling-PDF.exe ./build/launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe
          mv ./build/libs/Stirling-PDF-${{ steps.versionNumber.outputs.versionNumber }}.jar ./build/libs/Stirling-PDF${{ matrix.file_suffix }}.jar

      - name: Debug build artifacts
        run: |
          echo "Current Directory: $(pwd)"
          ls -R ./build/libs
          ls -R ./build/launch4j

      - name: Upload build artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: binaries${{ matrix.file_suffix }}
          path: |
            ./build/launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.*
            ./build/libs/Stirling-PDF${{ matrix.file_suffix }}.*

  sign_verify:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        disable_security: [true, false]
        include:
          - disable_security: false
            file_suffix: "-with-login"
          - disable_security: true
            file_suffix: ""
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Download build artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: binaries${{ matrix.file_suffix }}
      - name: Display structure of downloaded files
        run: ls -R

      - name: Install Cosign
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1

      - name: Generate key pair
        run: cosign generate-key-pair

      - name: Sign and generate attestations
        run: |
          cosign sign-blob \
            --key ./cosign.key \
            --yes \
            --output-signature ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar.sig \
            ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar

          cosign attest-blob \
            --predicate - \
            --key ./cosign.key \
            --yes \
            --output-attestation ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar.intoto.jsonl \
            ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar

          cosign verify-blob \
            --key ./cosign.pub \
            --signature ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar.sig \
            ./libs/Stirling-PDF${{ matrix.file_suffix }}.jar

          cosign sign-blob \
            --key ./cosign.key \
            --yes \
            --output-signature ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe.sig \
            ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe

          cosign attest-blob \
            --predicate - \
            --key ./cosign.key \
            --yes \
            --output-attestation ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe.intoto.jsonl \
            ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe

          cosign verify-blob \
            --key ./cosign.pub \
            --signature ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe.sig \
            ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.exe

      - name: Upload signed artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: signed${{ matrix.file_suffix }}
          path: |
            ./libs/Stirling-PDF${{ matrix.file_suffix }}.*
            ./launch4j/Stirling-PDF-Server${{ matrix.file_suffix }}.*

  release:
    needs: [build, sign_verify]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    strategy:
      matrix:
        disable_security: [true, false]
        include:
          - disable_security: false
            file_suffix: "-with-login"
          - disable_security: true
            file_suffix: ""
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Download signed artifacts
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: signed${{ matrix.file_suffix }}

      - name: Upload binaries, attestations and signatures to Release and create GitHub Release
        uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8 # v2.3.2
        with:
          tag_name: v${{ needs.build.outputs.version }}
          generate_release_notes: true
          files: |
            ./libs/Stirling-PDF*
            ./launch4j/Stirling-PDF-Server*
