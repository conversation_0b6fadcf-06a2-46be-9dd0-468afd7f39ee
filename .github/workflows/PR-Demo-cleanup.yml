name: PR Deployment cleanup

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]

permissions:
  contents: read

env:
  SERVER_IP: ${{ secrets.VPS_IP }} # Add this to your GitHub secrets
  CLEANUP_PERFORMED: "false" # Add flag to track if cleanup occurred

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    if: github.event.action == 'closed'

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.VPS_SSH_KEY }}" > ../private.key
          sudo chmod 600 ../private.key

      - name: Cleanup PR deployment
        id: cleanup
        run: |
          ssh -i ../private.key -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -T ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_HOST }} << 'ENDSSH'
            if [ -d "/stirling/PR-${{ github.event.pull_request.number }}" ]; then
              echo "Found PR directory, proceeding with cleanup..."

              # Stop and remove containers
              cd /stirling/PR-${{ github.event.pull_request.number }}
              docker-compose down || true

              # Go back to root before removal
              cd /

              # Remove PR-specific directories
              rm -rf /stirling/PR-${{ github.event.pull_request.number }}

              # Remove the Docker image
              docker rmi --no-prune ${{ secrets.DOCKER_HUB_USERNAME }}/test:pr-${{ github.event.pull_request.number }} || true

              echo "PERFORMED_CLEANUP"
            else
              echo "PR directory not found, nothing to clean up"
              echo "NO_CLEANUP_NEEDED"
            fi
          ENDSSH
