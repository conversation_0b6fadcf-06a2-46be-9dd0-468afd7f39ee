name: Sync Files

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - "build.gradle"
      - "README.md"
      - "stirling-pdf/src/main/resources/messages_*.properties"
      - "stirling-pdf/src/main/resources/static/3rdPartyLicenses.json"
      - "scripts/ignore_translation.toml"

permissions:
  contents: read

jobs:
  sync-files:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup GitHub App Bot
        id: setup-bot
        uses: ./.github/actions/setup-bot
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: "3.12"
          cache: "pip" # caching pip dependencies

      - name: Sync translation property files
        run: |
          python .github/scripts/check_language_properties.py --reference-file "stirling-pdf/src/main/resources/messages_en_GB.properties" --branch main

      - name: Commit translation files
        run: |
          git add stirling-pdf/src/main/resources/messages_*.properties
          git diff --staged --quiet || git commit -m ":memo: Sync translation files" || echo "No changes detected"

      - name: Install dependencies
        run: pip install --require-hashes -r ./.github/scripts/requirements_sync_readme.txt

      - name: Sync README.md
        run: |
          python scripts/counter_translation.py

      - name: Run git add
        run: |
          git add README.md scripts/ignore_translation.toml
          git diff --staged --quiet || git commit -m ":memo: Sync README.md & scripts/ignore_translation.toml" || echo "No changes detected"

      - name: Create Pull Request
        if: always()
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ steps.setup-bot.outputs.token }}
          commit-message: Update files
          committer: ${{ steps.setup-bot.outputs.committer }}
          author: ${{ steps.setup-bot.outputs.committer }}
          signoff: true
          branch: sync_readme
          title: ":globe_with_meridians: Sync Translations + Update README Progress Table"
          body: |
            ### Description of Changes

            This Pull Request was automatically generated to synchronize updates to translation files and documentation. Below are the details of the changes made:

            #### **1. Synchronization of Translation Files**
            - Updated translation files (`messages_*.properties`) to reflect changes in the reference file `messages_en_GB.properties`.
            - Ensured consistency and synchronization across all supported language files.
            - Highlighted any missing or incomplete translations.

            #### **2. Update README.md**
            - Generated the translation progress table in `README.md`.
            - Added a summary of the current translation status for all supported languages.
            - Included up-to-date statistics on translation coverage.

            #### **Why these changes are necessary**
            - Keeps translation files aligned with the latest reference updates.
            - Ensures the documentation reflects the current translation progress.

            ---

            Auto-generated by [create-pull-request][1].

            [1]: https://github.com/peter-evans/create-pull-request
          draft: false
          delete-branch: true
          labels: github-actions
          sign-commits: true
          add-paths: |
            README.md
              stirling-pdf/src/main/resources/messages_*.properties
