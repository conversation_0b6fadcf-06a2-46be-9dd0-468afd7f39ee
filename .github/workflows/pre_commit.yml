name: Pre-commit

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * 1"

permissions:
  contents: read

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Setup GitHub App Bot
        id: setup-bot
        uses: ./.github/actions/setup-bot
        with:
          app-id: ${{ secrets.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.12
          cache: 'pip' # caching pip dependencies

      - name: Run Pre-Commit Hooks
        run: |
          pip install --require-hashes -r ./.github/scripts/requirements_pre_commit.txt

      - run: pre-commit run --all-files -c .pre-commit-config.yaml
        continue-on-error: true

      - name: git add
        run: |
          git add .
          git diff --staged --quiet || echo "CHANGES_DETECTED=true" >> $GITHUB_ENV

      - name: Create Pull Request
        if: env.CHANGES_DETECTED == 'true'
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: ${{ steps.setup-bot.outputs.token }}
          commit-message: ":file_folder: pre-commit"
          committer: ${{ steps.setup-bot.outputs.committer }}
          author: ${{ steps.setup-bot.outputs.committer }}
          signoff: true
          branch: pre-commit
          title: "🤖 format everything with pre-commit by ${{ steps.setup-bot.outputs.app-slug }}"
          body: |
            Auto-generated by [create-pull-request][1] with **${{ steps.setup-bot.outputs.app-slug }}**

            [1]: https://github.com/peter-evans/create-pull-request
          draft: false
          delete-branch: true
          labels: github-actions
          sign-commits: true
