name: Run Sonarqube

on:
  push:
    branches:
      - master
  pull_request_target:
    branches:
      - main
  workflow_dispatch:

permissions:
  pull-requests: read
  actions: read

jobs:
  sonarqube:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1

      - name: Build and analyze with Gradle
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          DISABLE_ADDITIONAL_FEATURES: false
          STIRLING_PDF_DESKTOP_UI: true
        run: |
          ./gradlew clean build sonar \
            -Dsonar.projectKey=Stirling-Tools_Stirling-PDF \
            -Dsonar.organization=stirling-tools \
            -Dsonar.host.url=https://sonarcloud.io \
            -Dsonar.login=${SONAR_TOKEN} \
            -Dsonar.log.level=DEBUG \
            --info

      - name: Upload Problems Report on Failure
        if: failure()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: gradle-problems-report
          path: build/reports/problems/problems-report.html
          retention-days: 7

      - name: Upload Sonar Logs on Failure
        if: failure()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: sonar-logs
          path: |
            .scannerwork/report-task.txt
            build/sonar/
          retention-days: 7
