version: 1
labels:

  - label: "Bugfix"
    title: '^fix:.*'

  - label: "enhancement"
    title: '^feat:.*'

  - label: "build"
    title: '^build:.*'

  - label: "chore"
    title: '^chore:.*'

  - label: "ci"
    title: '^ci:.*'

  - label: "perf"
    title: '^perf:.*'

  - label: "refactor"
    title: '^refactor:.*'

  - label: "revert"
    title: '^revert:.*'

  - label: "style"
    title: '^style:.*'

  - label: "Documentation"
    title: '^docs:.*'

  - label: 'API'
    title: '.*openapi.*'

  - label: 'Translation'
    files:
      - 'stirling-pdf/src/main/resources/messages_[a-zA-Z_]{2}_[a-zA-Z_]{2,7}.properties'
      - 'scripts/ignore_translation.toml'
      - 'stirling-pdf/src/main/resources/templates/fragments/languages.html'
      - '.github/scripts/check_language_properties.py'

  - label: 'Front End'
    files:
      - 'stirling-pdf/src/main/resources/templates/.*'
      - 'proprietary/src/main/resources/templates/.*'
      - 'stirling-pdf/src/main/resources/static/.*'
      - 'proprietary/src/main/resources/static/.*'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/web/.*'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/UI/.*'
      - 'proprietary/src/main/java/stirling/software/proprietary/security/controller/web/.*'

  - label: 'Java'
    files:
      - 'common/src/main/java/.*.java'
      - 'proprietary/src/main/java/.*.java'
      - 'stirling-pdf/src/main/java/.*.java'

  - label: 'Back End'
    files:
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/config/.*'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/.*'
      - 'stirling-pdf/src/main/resources/settings.yml.template'
      - 'stirling-pdf/src/main/resources/application.properties'
      - 'stirling-pdf/src/main/resources/banner.txt'
      - 'scripts/png_to_webp.py'
      - 'split_photos.py'
      - 'application.properties'

  - label: 'Security'
    files:
      - 'proprietary/src/main/java/stirling/software/proprietary/security/.*'
      - 'scripts/download-security-jar.sh'
      - '.github/workflows/dependency-review.yml'
      - '.github/workflows/scorecards.yml'

  - label: 'API'
    files:
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/config/OpenApiConfig.java'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/web/MetricsController.java'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/controller/api/.*'
      - 'stirling-pdf/src/main/java/stirling/software/SPDF/model/api/.*'
      - 'proprietary/src/main/java/stirling/software/proprietary/security/controller/api/.*'
      - 'scripts/png_to_webp.py'
      - 'split_photos.py'
      - '.github/workflows/swagger.yml'

  - label: 'Documentation'
    files:
      - '.*.md'
      - 'scripts/counter_translation.py'
      - 'scripts/ignore_translation.toml'

  - label: 'Docker'
    files:
      - '.github/workflows/build.yml'
      - '.github/workflows/push-docker.yml'
      - 'Dockerfile'
      - 'Dockerfile.fat'
      - 'Dockerfile.ultra-lite'
      - 'exampleYmlFiles/.*.yml'
      - 'scripts/download-security-jar.sh'
      - 'scripts/init.sh'
      - 'scripts/init-without-ocr.sh'
      - 'scripts/installFonts.sh'
      - 'test.sh'
      - 'test2.sh'

  - label: 'Devtools'
    files:
      - '.devcontainer/.*'
      - 'Dockerfile.dev'
      - '.vscode/.*'
      - '.editorconfig'
      - '.pre-commit-config'
      - '.github/workflows/pre_commit.yml'
      - 'HowToAddNewLanguage.md'

  - label: 'Test'
    files:
      - 'common/src/test/.*'
      - 'proprietary/src/test/.*'
      - 'stirling-pdf/src/test/.*'
      - 'testing/.*'
      - '.github/workflows/scorecards.yml'
      - 'exampleYmlFiles/test_cicd.yml'

  - label: 'Github'
    files:
      - '.github/.*'

  - label: 'Gradle'
    files:
      - 'gradle/.*'
      - 'gradlew'
      - 'gradlew.bat'
      - 'settings.gradle'
      - 'build.gradle'
      - 'common/build.gradle'
      - 'proprietary/build.gradle'
      - 'stirling-pdf/build.gradle'
