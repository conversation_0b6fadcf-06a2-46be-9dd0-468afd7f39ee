package stirling.software.SPDF.controller.api.miniprogram.service;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

import javax.imageio.ImageIO;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.encryption.AccessPermission;
import org.apache.pdfbox.pdmodel.encryption.StandardProtectionPolicy;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.util.Matrix;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import stirling.software.SPDF.controller.api.miniprogram.request.WechatCompressRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatHtmlToPdfRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatImageToPdfRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatMergeRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatOfficeToPdfRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatPasswordRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatPdfToHtmlRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatPdfToImageRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatPdfToWordRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatRearrangeRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatRemovePagesRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatRemovePasswordRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatRotateRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatSplitRequest;
import stirling.software.SPDF.controller.api.miniprogram.request.WechatWatermarkRequest;
import stirling.software.SPDF.controller.api.miniprogram.response.WechatFileResponse;
import stirling.software.SPDF.controller.api.miniprogram.util.Base64MultipartFile;
import stirling.software.common.configuration.RuntimePathConfig;
import stirling.software.common.model.ApplicationProperties;
import stirling.software.common.model.api.converters.HTMLToPdfRequest;
import stirling.software.common.service.CustomPDFDocumentFactory;
import stirling.software.common.util.*;
import stirling.software.common.util.ProcessExecutor.ProcessExecutorResult;

/** 微信小程序文件处理服务 专门处理微信小程序的文件操作，统一使用JSON格式交互 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WechatFileService {

    private final CustomPDFDocumentFactory pdfDocumentFactory;
    private final TempFileManager tempFileManager;
    private final RuntimePathConfig runtimePathConfig;
    private final ApplicationProperties applicationProperties;

    /** 处理水印添加 */
    public WechatFileResponse processWatermark(WechatWatermarkRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始处理微信小程序水印请求: {}", request.getFileName());

            // 1. 验证请求参数
            request.validate();

            // 2. 转换base64为MultipartFile
            MultipartFile pdfFile =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());
            log.debug("文件转换完成，大小: {} bytes", pdfFile.getSize());

            // 3. 加载PDF文档
            PDDocument document = pdfDocumentFactory.load(pdfFile);
            log.debug("PDF文档加载完成，页数: {}", document.getNumberOfPages());

            // 4. 处理每一页
            for (PDPage page : document.getPages()) {
                PDPageContentStream contentStream =
                        new PDPageContentStream(
                                document, page, PDPageContentStream.AppendMode.APPEND, true, true);

                // 设置透明度
                PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
                graphicsState.setNonStrokingAlphaConstant(request.getOpacity());
                contentStream.setGraphicsStateParameters(graphicsState);

                if ("text".equalsIgnoreCase(request.getWatermarkType())) {
                    addTextWatermark(contentStream, request, document, page);
                } else if ("image".equalsIgnoreCase(request.getWatermarkType())) {
                    addImageWatermark(contentStream, request, document, page);
                }

                contentStream.close();
            }

            // 5. 如果需要转换为图像
            if (Boolean.TRUE.equals(request.getConvertPDFToImage())) {
                PDDocument convertedPdf = PdfUtils.convertPdfToPdfImage(document);
                document.close();
                document = convertedPdf;
                log.debug("PDF转图像完成");
            }

            // 6. 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            document.close();

            // 7. 生成输出文件名
            String outputFileName =
                    request.getFileName().replaceFirst("[.][^.]+$", "") + "_watermarked.pdf";

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("水印处理完成: {}, 耗时: {}ms", outputFileName, processingTime);

            return WechatFileResponse.success(
                    outputFileName, baos.toByteArray(), "application/pdf", processingTime);

        } catch (Exception e) {
            log.error("水印处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("水印处理失败", e);
        }
    }

    /** 将base64数据转换为MultipartFile */
    public MultipartFile createMultipartFileFromBase64(String base64Data, String fileName) {
        try {
            byte[] fileBytes = Base64.getDecoder().decode(base64Data);
            return new Base64MultipartFile(fileBytes, fileName);
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的base64数据: " + e.getMessage(), e);
        }
    }

    /** 添加文字水印 */
    private void addTextWatermark(
            PDPageContentStream contentStream,
            WechatWatermarkRequest request,
            PDDocument document,
            PDPage page)
            throws IOException {
        // 复用原有的文字水印逻辑
        String resourceDir = getResourceDir(request.getAlphabet());
        PDFont font = loadFont(document, resourceDir);

        contentStream.setFont(font, request.getFontSize());

        // 设置颜色
        Color watermarkColor = parseColor(request.getCustomColor());
        contentStream.setNonStrokingColor(watermarkColor);

        // 处理多行文本
        String[] textLines = request.getWatermarkText().split("\\\\n");
        float maxLineWidth = 0;

        for (String line : textLines) {
            maxLineWidth = Math.max(maxLineWidth, font.getStringWidth(line));
        }

        // 计算水印尺寸和位置
        float watermarkWidth =
                request.getWidthSpacer() + maxLineWidth * request.getFontSize() / 1000;
        float watermarkHeight =
                request.getHeightSpacer() + request.getFontSize() * textLines.length;
        float pageWidth = page.getMediaBox().getWidth();
        float pageHeight = page.getMediaBox().getHeight();

        // 计算旋转后的尺寸
        float radians = (float) Math.toRadians(request.getRotation());
        float newWatermarkWidth =
                (float)
                        (Math.abs(watermarkWidth * Math.cos(radians))
                                + Math.abs(watermarkHeight * Math.sin(radians)));
        float newWatermarkHeight =
                (float)
                        (Math.abs(watermarkWidth * Math.sin(radians))
                                + Math.abs(watermarkHeight * Math.cos(radians)));

        // 计算行列数
        int watermarkRows = (int) (pageHeight / newWatermarkHeight + 1);
        int watermarkCols = (int) (pageWidth / newWatermarkWidth + 1);

        // 添加文字水印
        for (int i = 0; i <= watermarkRows; i++) {
            for (int j = 0; j <= watermarkCols; j++) {
                contentStream.beginText();
                contentStream.setTextMatrix(
                        Matrix.getRotateInstance(
                                radians, j * newWatermarkWidth, i * newWatermarkHeight));

                for (String line : textLines) {
                    contentStream.showText(line);
                    contentStream.newLineAtOffset(0, -request.getFontSize());
                }

                contentStream.endText();
            }
        }
    }

    /** 添加图片水印 */
    private void addImageWatermark(
            PDPageContentStream contentStream,
            WechatWatermarkRequest request,
            PDDocument document,
            PDPage page)
            throws IOException {
        // 解码base64图片数据
        byte[] imageBytes = Base64.getDecoder().decode(request.getWatermarkImageData());
        BufferedImage image = ImageIO.read(new java.io.ByteArrayInputStream(imageBytes));

        // 计算尺寸
        float aspectRatio = (float) image.getWidth() / (float) image.getHeight();
        float desiredPhysicalHeight = request.getFontSize();
        float desiredPhysicalWidth = desiredPhysicalHeight * aspectRatio;

        // 转换为PDImageXObject
        PDImageXObject xobject = LosslessFactory.createFromImage(document, image);

        // 计算行列数
        float pageWidth = page.getMediaBox().getWidth();
        float pageHeight = page.getMediaBox().getHeight();
        int watermarkRows =
                (int)
                        ((pageHeight + request.getHeightSpacer())
                                / (desiredPhysicalHeight + request.getHeightSpacer()));
        int watermarkCols =
                (int)
                        ((pageWidth + request.getWidthSpacer())
                                / (desiredPhysicalWidth + request.getWidthSpacer()));

        // 添加图片水印
        for (int i = 0; i < watermarkRows; i++) {
            for (int j = 0; j < watermarkCols; j++) {
                float x = j * (desiredPhysicalWidth + request.getWidthSpacer());
                float y = i * (desiredPhysicalHeight + request.getHeightSpacer());

                contentStream.saveGraphicsState();

                // 旋转变换
                contentStream.transform(
                        Matrix.getTranslateInstance(
                                x + desiredPhysicalWidth / 2, y + desiredPhysicalHeight / 2));
                contentStream.transform(
                        Matrix.getRotateInstance(Math.toRadians(request.getRotation()), 0, 0));
                contentStream.transform(
                        Matrix.getTranslateInstance(
                                -desiredPhysicalWidth / 2, -desiredPhysicalHeight / 2));

                contentStream.drawImage(xobject, 0, 0, desiredPhysicalWidth, desiredPhysicalHeight);
                contentStream.restoreGraphicsState();
            }
        }
    }

    /** 获取字体资源目录 */
    private String getResourceDir(String alphabet) {
        switch (alphabet) {
            case "arabic":
                return "static/fonts/NotoSansArabic-Regular.ttf";
            case "japanese":
                return "static/fonts/Meiryo.ttf";
            case "korean":
                return "static/fonts/malgun.ttf";
            case "chinese":
                return "static/fonts/SimSun.ttf";
            case "thai":
                return "static/fonts/NotoSansThai-Regular.ttf";
            case "roman":
            default:
                return "static/fonts/NotoSans-Regular.ttf";
        }
    }

    /** 加载字体 */
    private PDFont loadFont(PDDocument document, String resourceDir) throws IOException {
        if (resourceDir.isEmpty()) {
            return new PDType1Font(Standard14Fonts.FontName.HELVETICA);
        }

        ClassPathResource classPathResource = new ClassPathResource(resourceDir);
        String fileExtension = resourceDir.substring(resourceDir.lastIndexOf("."));
        File tempFile = Files.createTempFile("NotoSansFont", fileExtension).toFile();

        try (InputStream is = classPathResource.getInputStream();
                FileOutputStream os = new FileOutputStream(tempFile)) {
            IOUtils.copy(is, os);
            return PDType0Font.load(document, tempFile);
        } finally {
            if (tempFile != null) {
                Files.deleteIfExists(tempFile.toPath());
            }
        }
    }

    /** 解析颜色 */
    private Color parseColor(String colorString) {
        try {
            if (!colorString.startsWith("#")) {
                colorString = "#" + colorString;
            }
            return Color.decode(colorString);
        } catch (NumberFormatException e) {
            return Color.LIGHT_GRAY;
        }
    }

    /** 处理密码保护添加 */
    public WechatFileResponse processAddPassword(WechatPasswordRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始处理密码保护添加: 文件={}, 密钥长度={}", request.getFileName(), request.getKeyLength());

            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            MultipartFile pdfFile =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 加载PDF文档
            PDDocument document = pdfDocumentFactory.load(pdfFile);

            // 设置访问权限
            AccessPermission accessPermission = new AccessPermission();
            accessPermission.setCanPrint(
                    request.getAllowPrinting() != null ? request.getAllowPrinting() : true);
            accessPermission.setCanExtractContent(
                    request.getAllowCopy() != null ? request.getAllowCopy() : false);
            accessPermission.setCanModify(
                    request.getAllowModify() != null ? request.getAllowModify() : false);
            accessPermission.setCanModifyAnnotations(
                    request.getAllowAnnotations() != null ? request.getAllowAnnotations() : false);
            accessPermission.setCanFillInForm(
                    request.getAllowFormFilling() != null ? request.getAllowFormFilling() : false);
            accessPermission.setCanExtractForAccessibility(true); // 保持可访问性

            // 创建保护策略
            String ownerPassword = request.getEffectiveOwnerPassword();
            String userPassword = request.getEffectivePassword();

            StandardProtectionPolicy protectionPolicy =
                    new StandardProtectionPolicy(ownerPassword, userPassword, accessPermission);

            // 只有在设置了密码时才设置加密密钥长度
            if (!ownerPassword.isEmpty() || !userPassword.isEmpty()) {
                protectionPolicy.setEncryptionKeyLength(request.getKeyLength());
            }

            // 应用保护策略
            document.protect(protectionPolicy);

            // 将处理后的文档转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            document.close();

            byte[] resultBytes = outputStream.toByteArray();
            String resultBase64 = Base64.getEncoder().encodeToString(resultBytes);

            // 生成输出文件名和消息
            String suffix;
            String message;
            if (ownerPassword.isEmpty() && userPassword.isEmpty()) {
                suffix = "_权限设置.pdf";
                message = "权限设置已完成";
            } else {
                suffix = "_加密.pdf";
                message = "文件已成功加密";
            }
            String outputFileName = request.getFileName().replaceFirst("\\.[^.]+$", suffix);

            long processingTime = System.currentTimeMillis() - startTime;

            log.info(
                    "密码保护/权限设置完成: 文件={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    resultBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    resultBase64, outputFileName, resultBytes.length, processingTime, message);

        } catch (IllegalArgumentException e) {
            log.warn("密码保护添加参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("密码保护添加失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("密码保护添加失败", e);
        }
    }

    /** 处理密码保护移除 */
    public WechatFileResponse processRemovePassword(WechatRemovePasswordRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始处理密码保护移除: 文件={}", request.getFileName());

            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            MultipartFile pdfFile =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 加载加密的PDF文档
            PDDocument document = pdfDocumentFactory.load(pdfFile, request.getPassword());

            // 检查文档是否真的被加密
            if (!document.isEncrypted()) {
                document.close();
                log.warn("文档未加密，无需移除密码保护: {}", request.getFileName());
                return WechatFileResponse.error("文档未加密，无需移除密码保护", null);
            }

            // 创建新的未加密文档
            PDDocument newDocument = new PDDocument();

            // 复制所有页面到新文档
            for (PDPage page : document.getPages()) {
                newDocument.addPage(page);
            }

            // 复制文档信息
            if (document.getDocumentInformation() != null) {
                newDocument.setDocumentInformation(document.getDocumentInformation());
            }

            document.close();

            // 将处理后的文档转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            newDocument.save(outputStream);
            newDocument.close();

            byte[] resultBytes = outputStream.toByteArray();
            String resultBase64 = Base64.getEncoder().encodeToString(resultBytes);

            // 生成输出文件名
            String outputFileName = request.getFileName().replaceFirst("\\.[^.]+$", "_解密.pdf");

            long processingTime = System.currentTimeMillis() - startTime;

            log.info(
                    "密码保护移除完成: 文件={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    resultBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    resultBase64, outputFileName, resultBytes.length, processingTime, "文件已成功解密");

        } catch (IllegalArgumentException e) {
            log.warn("密码保护移除参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("密码保护移除失败: {}", e.getMessage(), e);
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("password")) {
                return WechatFileResponse.error("密码错误，请检查密码是否正确", e);
            }
            return WechatFileResponse.error("密码保护移除失败", e);
        }
    }

    /** 处理PDF转图片 */
    public WechatFileResponse processPdfToImage(WechatPdfToImageRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF转图片: {}", request.getFileName());

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            byte[] fileBytes = Base64.getDecoder().decode(request.getFileData());
            MultipartFile pdfFile = new Base64MultipartFile(fileBytes, request.getFileName());

            // 构建转换参数
            String imageFormat = request.getImageFormat();
            String singleOrMultiple = request.getSingleOrMultiple();
            String colorType = request.getColorType();
            int dpi = request.getDpi();
            String pageNumbers = request.getPageNumbers();

            // 调用现有的PDF转图片逻辑
            byte[] pdfBytes = pdfFile.getBytes();
            String filename = pdfFile.getOriginalFilename();
            boolean singleImage = "single".equalsIgnoreCase(singleOrMultiple);

            // 处理颜色类型
            ImageType colorTypeResult = ImageType.RGB;
            if ("greyscale".equals(colorType)) {
                colorTypeResult = ImageType.GRAY;
            } else if ("blackwhite".equals(colorType)) {
                colorTypeResult = ImageType.BINARY;
            }

            // 执行转换
            byte[] result =
                    PdfUtils.convertFromPdf(
                            pdfDocumentFactory,
                            pdfBytes,
                            "webp".equalsIgnoreCase(imageFormat)
                                    ? "png"
                                    : imageFormat.toUpperCase(),
                            colorTypeResult,
                            singleImage,
                            dpi,
                            filename);

            if (result == null || result.length == 0) {
                log.error("PDF转图片结果为空: {}", filename);
                return WechatFileResponse.error("PDF转图片失败，结果为空", null);
            }

            // 构建响应
            String outputFileName =
                    filename.replaceFirst("[.][^.]+$", "")
                            + (singleImage ? "." + imageFormat : "_images.zip");
            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF转图片处理完成: {} -> {}, 耗时: {}ms",
                    request.getFileName(),
                    outputFileName,
                    processingTime);

            return WechatFileResponse.success(
                    base64Result, outputFileName, result.length, processingTime, "PDF转图片处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF转图片参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF转图片处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF转图片处理失败", e);
        }
    }

    /** 处理图片转PDF */
    public WechatFileResponse processImageToPdf(WechatImageToPdfRequest request) {
        long startTime = System.currentTimeMillis();
        log.info(
                "开始处理图片转PDF: {} 张图片",
                request.getImageFiles() != null ? request.getImageFiles().size() : 0);

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile数组
            List<MultipartFile> imageFiles = new ArrayList<>();
            for (WechatImageToPdfRequest.ImageData imageData : request.getImageFiles()) {
                byte[] fileBytes = Base64.getDecoder().decode(imageData.getFileData());
                MultipartFile imageFile =
                        new Base64MultipartFile(fileBytes, imageData.getFileName());
                imageFiles.add(imageFile);
            }

            MultipartFile[] fileArray = imageFiles.toArray(new MultipartFile[0]);

            // 构建转换参数
            String fitOption = request.getFitOption();
            String colorType = request.getColorType();
            boolean autoRotate = Boolean.TRUE.equals(request.getAutoRotate());

            // 处理默认值
            if (colorType == null || colorType.isBlank()) {
                colorType = "color";
            }
            if (fitOption == null || fitOption.isEmpty()) {
                fitOption = "fillPage";
            }

            // 执行转换
            byte[] result =
                    PdfUtils.imageToPdf(
                            fileArray, fitOption, autoRotate, colorType, pdfDocumentFactory);

            if (result == null || result.length == 0) {
                log.error("图片转PDF结果为空");
                return WechatFileResponse.error("图片转PDF失败，结果为空", null);
            }

            // 构建响应
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                outputFileName = "converted_images.pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "图片转PDF处理完成: {} -> {}, 耗时: {}ms",
                    request.getImageFiles().size(),
                    outputFileName,
                    processingTime);

            return WechatFileResponse.success(
                    base64Result, outputFileName, result.length, processingTime, "图片转PDF处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("图片转PDF参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("图片转PDF处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("图片转PDF处理失败", e);
        }
    }

    /** 处理PDF转Word */
    public WechatFileResponse processPdfToWord(WechatPdfToWordRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF转Word: {}", request.getFileName());

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            byte[] fileBytes = Base64.getDecoder().decode(request.getFileData());
            MultipartFile pdfFile = new Base64MultipartFile(fileBytes, request.getFileName());

            // 构建转换参数
            String outputFormat = request.getOutputFormat();
            if (outputFormat == null || outputFormat.trim().isEmpty()) {
                outputFormat = "docx";
            }

            // 执行转换
            PDFToFile pdfToFile = new PDFToFile();
            org.springframework.http.ResponseEntity<byte[]> response =
                    pdfToFile.processPdfToOfficeFormat(pdfFile, outputFormat, "writer_pdf_import");
            byte[] result = response.getBody();

            if (result == null || result.length == 0) {
                log.error("PDF转Word结果为空: {}", request.getFileName());
                return WechatFileResponse.error("PDF转Word失败，结果为空", null);
            }

            // 构建响应
            String outputFileName =
                    request.getFileName().replaceFirst("[.][^.]+$", "") + "." + outputFormat;
            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF转Word处理完成: {} -> {}, 耗时: {}ms",
                    request.getFileName(),
                    outputFileName,
                    processingTime);

            return WechatFileResponse.success(
                    base64Result, outputFileName, result.length, processingTime, "PDF转Word处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF转Word参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF转Word处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF转Word处理失败", e);
        }
    }

    /** 处理Office转PDF - 简化版本，直接使用PC端逻辑 */
    public WechatFileResponse processOfficeToPdf(WechatOfficeToPdfRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理Office转PDF: {}", request.getFileName());

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            byte[] fileBytes = Base64.getDecoder().decode(request.getFileData());
            MultipartFile officeFile = new Base64MultipartFile(fileBytes, request.getFileName());

            // 直接使用PC端的ConvertOfficeController逻辑
            log.info("使用PC端Office转PDF逻辑处理文件: {}", request.getFileName());

            // 复用PC端的convertToPdf方法逻辑
            byte[] result = convertOfficeToPdfSimplified(officeFile);

            if (result == null || result.length == 0) {
                log.error("Office转PDF结果为空: {}", request.getFileName());
                return WechatFileResponse.error("Office转PDF失败，结果为空", null);
            }

            // 构建响应
            String outputFileName = request.getFileName().replaceFirst("[.][^.]+$", "") + ".pdf";
            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "Office转PDF处理完成: {} -> {}, 大小: {}bytes, 耗时: {}ms",
                    request.getFileName(),
                    outputFileName,
                    result.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Result, outputFileName, result.length, processingTime, "Office转PDF处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("Office转PDF参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Office转PDF处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("Office转PDF处理失败", e);
        }
    }

    /** 简化的Office转PDF转换方法 - 复用PC端逻辑 */
    private byte[] convertOfficeToPdfSimplified(MultipartFile inputFile)
            throws IOException, InterruptedException {
        long stepStartTime = System.currentTimeMillis();
        log.info("=== 开始Office转PDF详细步骤 ===");

        // 检查文件扩展名
        String originalFilename = inputFile.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        log.info(
                "步骤1: 文件名检查完成，文件: {}, 耗时: {}ms",
                originalFilename,
                System.currentTimeMillis() - stepStartTime);

        // 创建临时输入文件
        stepStartTime = System.currentTimeMillis();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        Path tempInputFile = Files.createTempFile("input_", extension);
        inputFile.transferTo(tempInputFile);
        log.info(
                "步骤2: 临时输入文件创建完成，路径: {}, 大小: {}bytes, 耗时: {}ms",
                tempInputFile,
                Files.size(tempInputFile),
                System.currentTimeMillis() - stepStartTime);

        // 创建临时输出文件
        stepStartTime = System.currentTimeMillis();
        Path tempOutputFile = Files.createTempFile("output_", ".pdf");
        log.info(
                "步骤3: 临时输出文件创建完成，路径: {}, 耗时: {}ms",
                tempOutputFile,
                System.currentTimeMillis() - stepStartTime);

        try {
            // 运行LibreOffice命令 - 与PC端完全一致
            stepStartTime = System.currentTimeMillis();
            List<String> command =
                    new ArrayList<>(
                            Arrays.asList(
                                    runtimePathConfig.getUnoConvertPath(),
                                    "--port",
                                    "2003",
                                    "--convert-to",
                                    "pdf",
                                    "--filter-options",
                                    "ExportFormFields=false;FormsType=0;ExportBookmarks=false;ExportNotes=false",
                                    tempInputFile.toString(),
                                    tempOutputFile.toString()));
            log.info(
                    "步骤4: LibreOffice命令准备完成，命令: {}, 耗时: {}ms",
                    command,
                    System.currentTimeMillis() - stepStartTime);

            stepStartTime = System.currentTimeMillis();
            log.info("步骤5: 开始执行LibreOffice转换...");

            // 添加转换前的优化提示
            log.info("文档大小: {}KB, 预计转换时间: 1-3分钟", tempInputFile.toFile().length() / 1024);

            ProcessExecutorResult returnCode =
                    ProcessExecutor.getInstance(ProcessExecutor.Processes.LIBRE_OFFICE)
                            .runCommandWithOutputHandling(command);
            log.info(
                    "步骤5: LibreOffice转换完成，返回码: {}, 耗时: {}ms",
                    returnCode.getRc(),
                    System.currentTimeMillis() - stepStartTime);

            if (returnCode.getRc() != 0) {
                throw new RuntimeException("LibreOffice转换失败，返回码: " + returnCode.getRc());
            }

            // 读取转换后的PDF文件
            stepStartTime = System.currentTimeMillis();
            byte[] result = Files.readAllBytes(tempOutputFile);
            log.info(
                    "步骤6: PDF文件读取完成，大小: {}bytes, 耗时: {}ms",
                    result.length,
                    System.currentTimeMillis() - stepStartTime);

            // 使用PDFDocumentFactory处理结果 - 与PC端一致
            stepStartTime = System.currentTimeMillis();
            byte[] finalResult = pdfDocumentFactory.createNewBytesBasedOnOldDocument(result);
            log.info(
                    "步骤7: PDFDocumentFactory处理完成，最终大小: {}bytes, 耗时: {}ms",
                    finalResult.length,
                    System.currentTimeMillis() - stepStartTime);

            log.info("=== Office转PDF详细步骤完成 ===");
            return finalResult;

        } finally {
            // 清理临时文件
            stepStartTime = System.currentTimeMillis();
            Files.deleteIfExists(tempInputFile);
            Files.deleteIfExists(tempOutputFile);
            log.info("步骤8: 临时文件清理完成，耗时: {}ms", System.currentTimeMillis() - stepStartTime);
        }
    }

    /** 内部Office转PDF转换方法 */
    private byte[] convertOfficeToPdfInternal(File officeFile) throws Exception {
        // 检查文件扩展名
        String originalFilename = officeFile.getName();
        if (originalFilename == null
                || !isValidOfficeFileExtension(FilenameUtils.getExtension(originalFilename))) {
            throw new IllegalArgumentException("Invalid file extension");
        }

        // 准备输出文件路径
        Path tempOutputFile = Files.createTempFile("output_", ".pdf");

        try {
            // 运行LibreOffice命令
            List<String> command =
                    new ArrayList<>(
                            Arrays.asList(
                                    runtimePathConfig.getUnoConvertPath(),
                                    "--port",
                                    "2003",
                                    "--convert-to",
                                    "pdf",
                                    officeFile.getAbsolutePath(),
                                    tempOutputFile.toString()));

            ProcessExecutorResult returnCode =
                    ProcessExecutor.getInstance(ProcessExecutor.Processes.LIBRE_OFFICE)
                            .runCommandWithOutputHandling(command);

            if (returnCode.getRc() != 0) {
                throw new RuntimeException("LibreOffice转换失败，返回码: " + returnCode.getRc());
            }

            // 读取转换后的PDF文件
            byte[] result = Files.readAllBytes(tempOutputFile);

            // 使用PDFDocumentFactory处理结果
            return pdfDocumentFactory.createNewBytesBasedOnOldDocument(result);

        } finally {
            // 清理临时文件
            if (tempOutputFile != null) {
                Files.deleteIfExists(tempOutputFile);
            }
        }
    }

    /** 检查是否为有效的Office文件扩展名 */
    private boolean isValidOfficeFileExtension(String fileExtension) {
        if (fileExtension == null) return false;
        String ext = fileExtension.toLowerCase();
        return ext.equals("docx")
                || ext.equals("doc")
                || ext.equals("xlsx")
                || ext.equals("xls")
                || ext.equals("pptx")
                || ext.equals("ppt")
                || ext.equals("odt")
                || ext.equals("ods")
                || ext.equals("odp")
                || ext.equals("rtf")
                || ext.equals("txt");
    }

    /** 处理PDF转HTML */
    public WechatFileResponse processPdfToHtml(WechatPdfToHtmlRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF转HTML: {}", request.getFileName());

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            byte[] fileBytes = Base64.getDecoder().decode(request.getFileData());
            MultipartFile pdfFile = new Base64MultipartFile(fileBytes, request.getFileName());

            // 执行转换 - 直接使用PC端的逻辑
            PDFToFile pdfToFile = new PDFToFile();
            org.springframework.http.ResponseEntity<byte[]> response =
                    pdfToFile.processPdfToHtml(pdfFile);
            byte[] result = response.getBody();

            if (result == null || result.length == 0) {
                log.error("PDF转HTML结果为空: {}", request.getFileName());
                return WechatFileResponse.error("PDF转HTML失败，结果为空", null);
            }

            // 构建输出文件名 - PDF转HTML返回ZIP文件
            String outputFileName =
                    request.getFileName().replaceFirst("[.][^.]+$", "") + "_html.zip";
            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF转HTML处理完成: {} -> {}, 耗时: {}ms",
                    request.getFileName(),
                    outputFileName,
                    processingTime);

            WechatFileResponse wechatResponse =
                    WechatFileResponse.success(
                            base64Result,
                            outputFileName,
                            result.length,
                            processingTime,
                            "PDF转HTML处理成功");
            // 修正MIME类型为ZIP
            wechatResponse.setMimeType("application/zip");
            return wechatResponse;

        } catch (IllegalArgumentException e) {
            log.warn("PDF转HTML参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF转HTML处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF转HTML处理失败", e);
        }
    }

    /** 处理HTML转PDF */
    public WechatFileResponse processHtmlToPdf(WechatHtmlToPdfRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理HTML转PDF: {}", request.getFileName());

        try {
            // 验证请求参数
            request.validate();

            // 将base64数据转换为MultipartFile
            byte[] fileBytes = Base64.getDecoder().decode(request.getFileData());
            MultipartFile htmlFile = new Base64MultipartFile(fileBytes, request.getFileName());

            // 构建HTMLToPdfRequest
            HTMLToPdfRequest htmlToPdfRequest = new HTMLToPdfRequest();
            htmlToPdfRequest.setFileInput(htmlFile);
            htmlToPdfRequest.setZoom(request.getZoom());

            // 执行转换
            boolean disableSanitize =
                    Boolean.TRUE.equals(applicationProperties.getSystem().getDisableSanitize());

            byte[] result =
                    FileToPdf.convertHtmlToPdf(
                            runtimePathConfig.getWeasyPrintPath(),
                            htmlToPdfRequest,
                            htmlFile.getBytes(),
                            htmlFile.getOriginalFilename(),
                            disableSanitize,
                            tempFileManager);

            // 使用PDFDocumentFactory处理结果
            result = pdfDocumentFactory.createNewBytesBasedOnOldDocument(result);

            if (result == null || result.length == 0) {
                log.error("HTML转PDF结果为空: {}", request.getFileName());
                return WechatFileResponse.error("HTML转PDF失败，结果为空", null);
            }

            // 构建响应
            String outputFileName = request.getFileName().replaceFirst("[.][^.]+$", "") + ".pdf";
            String base64Result = Base64.getEncoder().encodeToString(result);

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "HTML转PDF处理完成: {} -> {}, 耗时: {}ms",
                    request.getFileName(),
                    outputFileName,
                    processingTime);

            return WechatFileResponse.success(
                    base64Result, outputFileName, result.length, processingTime, "HTML转PDF处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("HTML转PDF参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("HTML转PDF处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("HTML转PDF处理失败", e);
        }
    }

    /** 处理PDF合并请求 */
    public WechatFileResponse processMergePdfs(WechatMergeRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始处理PDF合并请求 ===");
        log.info("文件数量: {}", request.getFileDataList() != null ? request.getFileDataList().size() : 0);
        log.info("输出文件名: {}", request.getOutputFileName());
        log.info("排序类型: {}", request.getSortType());
        log.info("移除证书签名: {}", request.getRemoveCertSign());
        log.info("生成目录: {}", request.getGenerateToc());

        try {
            // 步骤1: 验证请求参数
            long stepStartTime = System.currentTimeMillis();
            log.info("步骤1: 开始验证请求参数...");

            if (request.getFileDataList() == null || request.getFileDataList().isEmpty()) {
                log.error("文件列表为空或null");
                return WechatFileResponse.error("文件列表不能为空", null);
            }

            if (request.getFileNameList() == null || request.getFileNameList().isEmpty()) {
                log.error("文件名列表为空或null");
                return WechatFileResponse.error("文件名列表不能为空", null);
            }

            if (request.getFileDataList().size() != request.getFileNameList().size()) {
                log.error("文件数据列表和文件名列表长度不匹配: {} vs {}",
                    request.getFileDataList().size(), request.getFileNameList().size());
                return WechatFileResponse.error("文件数据和文件名数量不匹配", null);
            }

            log.info("步骤1: 参数验证完成，耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤2: 创建合并后的PDF文档
            stepStartTime = System.currentTimeMillis();
            log.info("步骤2: 创建合并文档...");
            PDDocument mergedDocument = pdfDocumentFactory.createNewDocument();
            log.info("步骤2: 合并文档创建完成，耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤3: 逐个处理文件并合并
            List<String> fileDataList = request.getFileDataList();
            List<String> fileNameList = request.getFileNameList();
            int totalPages = 0;
            long totalInputSize = 0;
            List<PDDocument> sourceDocuments = new ArrayList<>(); // 保存所有源文档引用

            try {
                for (int i = 0; i < fileDataList.size(); i++) {
                    stepStartTime = System.currentTimeMillis();
                    String fileData = fileDataList.get(i);
                    String fileName = fileNameList.get(i);

                    log.info("步骤3.{}: 处理文件 {} - {}", i + 1, i + 1, fileName);

                    try {
                        // 检查base64数据
                        if (fileData == null || fileData.trim().isEmpty()) {
                            log.error("文件 {} 的base64数据为空", fileName);
                            return WechatFileResponse.error("文件 " + fileName + " 数据为空", null);
                        }

                        // 估算文件大小
                        long estimatedSize = (fileData.length() * 3) / 4; // base64解码后的大小估算
                        totalInputSize += estimatedSize;
                        log.info("文件 {} 估算大小: {}KB", fileName, estimatedSize / 1024);

                        // 转换base64为MultipartFile
                        MultipartFile file = createMultipartFileFromBase64(fileData, fileName);
                        log.info("文件 {} base64转换完成，实际大小: {}KB", fileName, file.getSize() / 1024);

                        // 加载PDF文档
                        PDDocument sourceDocument = pdfDocumentFactory.load(file);
                        sourceDocuments.add(sourceDocument); // 添加到列表中，稍后统一关闭

                        int pageCount = sourceDocument.getNumberOfPages();
                        totalPages += pageCount;
                        log.info("文件 {} 加载完成，页数: {}", fileName, pageCount);

                        // 将所有页面添加到合并文档中
                        for (PDPage page : sourceDocument.getPages()) {
                            mergedDocument.addPage(page);
                        }

                        log.info("步骤3.{}: 文件 {} 处理完成，耗时: {}ms", i + 1, fileName,
                            System.currentTimeMillis() - stepStartTime);

                    } catch (Exception e) {
                        log.error("处理文件 {} 时发生错误: {}", fileName, e.getMessage(), e);
                        return WechatFileResponse.error("处理文件 " + fileName + " 失败: " + e.getMessage(), e);
                    }
                }
            } finally {
                // 在finally块中关闭所有源文档，确保资源正确释放
                for (PDDocument doc : sourceDocuments) {
                    try {
                        if (doc != null) {
                            doc.close();
                        }
                    } catch (Exception e) {
                        log.warn("关闭源文档时发生错误: {}", e.getMessage());
                    }
                }
            }

            log.info("所有文件处理完成，总页数: {}, 总输入大小: {}KB", totalPages, totalInputSize / 1024);

            // 步骤4: 转换为字节数组
            stepStartTime = System.currentTimeMillis();
            log.info("步骤4: 开始保存合并文档...");
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            mergedDocument.save(baos);
            mergedDocument.close();

            byte[] mergedPdfBytes = baos.toByteArray();
            log.info("步骤4: 合并文档保存完成，输出大小: {}KB, 耗时: {}ms",
                mergedPdfBytes.length / 1024, System.currentTimeMillis() - stepStartTime);

            // 步骤5: 编码为base64
            stepStartTime = System.currentTimeMillis();
            log.info("步骤5: 开始base64编码...");
            String base64Data = Base64.getEncoder().encodeToString(mergedPdfBytes);
            log.info("步骤5: base64编码完成，耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤6: 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                outputFileName = "merged_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("=== PDF合并处理完成 ===");
            log.info("输出文件: {}", outputFileName);
            log.info("输出大小: {}KB", mergedPdfBytes.length / 1024);
            log.info("总页数: {}", totalPages);
            log.info("总耗时: {}ms", processingTime);
            log.info("压缩比: {:.2f}%", (double) mergedPdfBytes.length / totalInputSize * 100);

            return WechatFileResponse.success(
                    base64Data, outputFileName, mergedPdfBytes.length, processingTime, "PDF合并处理成功");

        } catch (IllegalArgumentException e) {
            log.error("PDF合并参数错误: {}", e.getMessage(), e);
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (OutOfMemoryError e) {
            log.error("PDF合并内存不足: {}", e.getMessage(), e);
            return WechatFileResponse.error("文件过大，内存不足，请减少文件数量或大小", e);
        } catch (Exception e) {
            log.error("PDF合并处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF合并处理失败: " + e.getMessage(), e);
        }
    }

    /** 处理PDF分割请求 */
    public WechatFileResponse processSplitPdf(WechatSplitRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF分割请求: 文件={}", request.getFileName());

        try {
            // 验证请求参数
            if (request.getFileData() == null || request.getFileData().trim().isEmpty()) {
                return WechatFileResponse.error("文件数据不能为空", null);
            }

            // 转换base64文件为MultipartFile
            MultipartFile file =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 加载PDF文档
            PDDocument sourceDocument = pdfDocumentFactory.load(file);

            // 解析页面范围
            String[] pageOrderArr = request.getPageNumbers().split(",");
            List<Integer> pageNumbers =
                    GeneralUtils.parsePageList(
                            pageOrderArr, sourceDocument.getNumberOfPages(), false);

            // 创建分割后的PDF文档
            PDDocument splitDocument =
                    pdfDocumentFactory.createNewDocumentBasedOnOldDocument(sourceDocument);

            // 添加指定页面到新文档
            for (int pageIndex : pageNumbers) {
                PDPage page = sourceDocument.getPage(pageIndex);
                splitDocument.addPage(page);
            }

            sourceDocument.close();

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            splitDocument.save(baos);
            splitDocument.close();

            byte[] splitPdfBytes = baos.toByteArray();
            String base64Data = Base64.getEncoder().encodeToString(splitPdfBytes);

            // 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                String baseName = request.getFileName().replaceAll("\\.pdf$", "");
                outputFileName = baseName + "_split_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF分割处理完成: 文件={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    splitPdfBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Data, outputFileName, splitPdfBytes.length, processingTime, "PDF分割处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF分割参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF分割处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF分割处理失败", e);
        }
    }

    /** 处理PDF压缩请求 */
    public WechatFileResponse processCompressPdf(WechatCompressRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF压缩请求: 文件={}", request.getFileName());

        try {
            // 验证请求参数
            if (request.getFileData() == null || request.getFileData().trim().isEmpty()) {
                return WechatFileResponse.error("文件数据不能为空", null);
            }

            // 转换base64文件为MultipartFile
            MultipartFile file =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // PDF压缩功能比较复杂，需要外部工具支持
            // 暂时返回原文件，提示压缩功能开发中
            log.warn("PDF压缩功能暂时不可用，返回原文件");

            // 读取原文件内容
            byte[] originalBytes = file.getBytes();
            String base64Data = Base64.getEncoder().encodeToString(originalBytes);

            // 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                String baseName = request.getFileName().replaceAll("\\.pdf$", "");
                outputFileName = baseName + "_compressed_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF压缩处理完成(暂时返回原文件): 文件={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    originalBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Data,
                    outputFileName,
                    originalBytes.length,
                    processingTime,
                    "PDF压缩处理完成(暂时返回原文件)");

        } catch (IllegalArgumentException e) {
            log.warn("PDF压缩参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF压缩处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF压缩处理失败", e);
        }
    }

    /** 处理PDF旋转请求 */
    public WechatFileResponse processRotatePdf(WechatRotateRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF旋转请求: 文件={}", request.getFileName());

        try {
            // 验证请求参数
            if (request.getFileData() == null || request.getFileData().trim().isEmpty()) {
                return WechatFileResponse.error("文件数据不能为空", null);
            }

            // 转换base64文件为MultipartFile
            MultipartFile file =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 验证角度是90的倍数
            Integer angle = request.getAngle();
            if (angle % 90 != 0) {
                return WechatFileResponse.error("角度必须是90的倍数", null);
            }

            // 加载PDF文档
            PDDocument document = pdfDocumentFactory.load(file);

            // 旋转所有页面
            for (PDPage page : document.getPages()) {
                page.setRotation(page.getRotation() + angle);
            }

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            document.close();

            byte[] rotatedPdfBytes = baos.toByteArray();
            String base64Data = Base64.getEncoder().encodeToString(rotatedPdfBytes);

            // 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                String baseName = request.getFileName().replaceAll("\\.pdf$", "");
                outputFileName = baseName + "_rotated_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF旋转处理完成: 文件={}, 角度={}°, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    angle,
                    rotatedPdfBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Data,
                    outputFileName,
                    rotatedPdfBytes.length,
                    processingTime,
                    "PDF旋转处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF旋转参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF旋转处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF旋转处理失败", e);
        }
    }

    /** 处理PDF删除页面请求 */
    public WechatFileResponse processRemovePages(WechatRemovePagesRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF删除页面请求: 文件={}", request.getFileName());

        try {
            // 验证请求参数
            if (request.getFileData() == null || request.getFileData().trim().isEmpty()) {
                return WechatFileResponse.error("文件数据不能为空", null);
            }

            // 转换base64文件为MultipartFile
            MultipartFile file =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 加载PDF文档
            PDDocument document = pdfDocumentFactory.load(file);

            // 解析要删除的页面
            String[] pageOrderArr = request.getPagesToDelete().split(",");
            List<Integer> pagesToRemove =
                    GeneralUtils.parsePageList(pageOrderArr, document.getNumberOfPages(), false);

            // 按降序排序，从后往前删除，避免索引变化
            Collections.sort(pagesToRemove);
            for (int i = pagesToRemove.size() - 1; i >= 0; i--) {
                int pageIndex = pagesToRemove.get(i);
                document.removePage(pageIndex);
            }

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            document.close();

            byte[] processedPdfBytes = baos.toByteArray();
            String base64Data = Base64.getEncoder().encodeToString(processedPdfBytes);

            // 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                String baseName = request.getFileName().replaceAll("\\.pdf$", "");
                outputFileName = baseName + "_removed_pages_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF删除页面处理完成: 文件={}, 删除页面={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    request.getPagesToDelete(),
                    processedPdfBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Data,
                    outputFileName,
                    processedPdfBytes.length,
                    processingTime,
                    "PDF删除页面处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF删除页面参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF删除页面处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF删除页面处理失败", e);
        }
    }

    /** 处理PDF重排页面请求 */
    public WechatFileResponse processRearrangePages(WechatRearrangeRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理PDF重排页面请求: 文件={}", request.getFileName());

        try {
            // 验证请求参数
            if (request.getFileData() == null || request.getFileData().trim().isEmpty()) {
                return WechatFileResponse.error("文件数据不能为空", null);
            }

            // 转换base64文件为MultipartFile
            MultipartFile file =
                    createMultipartFileFromBase64(request.getFileData(), request.getFileName());

            // 加载PDF文档
            PDDocument sourceDocument = pdfDocumentFactory.load(file);

            // 解析页面顺序
            String[] pageOrderArr = request.getPageOrder().split(",");
            List<Integer> pageOrder =
                    GeneralUtils.parsePageList(
                            pageOrderArr, sourceDocument.getNumberOfPages(), false);

            // 创建新文档
            PDDocument rearrangedDocument =
                    pdfDocumentFactory.createNewDocumentBasedOnOldDocument(sourceDocument);

            // 按指定顺序添加页面
            for (int pageIndex : pageOrder) {
                PDPage page = sourceDocument.getPage(pageIndex);
                rearrangedDocument.addPage(page);
            }

            sourceDocument.close();

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            rearrangedDocument.save(baos);
            rearrangedDocument.close();

            byte[] processedPdfBytes = baos.toByteArray();
            String base64Data = Base64.getEncoder().encodeToString(processedPdfBytes);

            // 生成输出文件名
            String outputFileName = request.getOutputFileName();
            if (outputFileName == null || outputFileName.trim().isEmpty()) {
                String baseName = request.getFileName().replaceAll("\\.pdf$", "");
                outputFileName = baseName + "_rearranged_" + System.currentTimeMillis() + ".pdf";
            }
            if (!outputFileName.toLowerCase().endsWith(".pdf")) {
                outputFileName += ".pdf";
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.info(
                    "PDF重排页面处理完成: 文件={}, 页面顺序={}, 大小={}bytes, 耗时={}ms",
                    outputFileName,
                    request.getPageOrder(),
                    processedPdfBytes.length,
                    processingTime);

            return WechatFileResponse.success(
                    base64Data,
                    outputFileName,
                    processedPdfBytes.length,
                    processingTime,
                    "PDF重排页面处理成功");

        } catch (IllegalArgumentException e) {
            log.warn("PDF重排页面参数错误: {}", e.getMessage());
            return WechatFileResponse.error("参数错误: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("PDF重排页面处理失败: {}", e.getMessage(), e);
            return WechatFileResponse.error("PDF重排页面处理失败", e);
        }
    }
}
