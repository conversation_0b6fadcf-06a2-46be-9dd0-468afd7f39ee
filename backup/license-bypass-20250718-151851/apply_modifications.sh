#!/bin/bash

# Stirling PDF 许可证验证绕过应用脚本
# 创建时间: $(date)
# 用途: 重新应用许可证验证绕过修改

echo "=== Stirling PDF 许可证验证绕过应用脚本 ==="
echo "此脚本将重新应用许可证验证绕过修改"
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "备份目录: $SCRIPT_DIR"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 检查项目文件是否存在
PREMIUM_FILE="$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/config/PremiumEndpointAspect.java"
ENTERPRISE_FILE="$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/config/EnterpriseEndpointAspect.java"
LICENSE_FILE="$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/configuration/ee/LicenseKeyChecker.java"

if [ ! -f "$PREMIUM_FILE" ]; then
    echo "错误: 找不到 PremiumEndpointAspect.java"
    exit 1
fi

if [ ! -f "$ENTERPRISE_FILE" ]; then
    echo "错误: 找不到 EnterpriseEndpointAspect.java"
    exit 1
fi

if [ ! -f "$LICENSE_FILE" ]; then
    echo "错误: 找不到 LicenseKeyChecker.java"
    exit 1
fi

echo "所有目标文件检查完成"
echo ""

# 询问用户确认
read -p "确定要应用许可证验证绕过修改吗? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始应用修改..."

# 创建当前时间戳的备份
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
CURRENT_BACKUP_DIR="$PROJECT_ROOT/backup/current-backup-$TIMESTAMP"
mkdir -p "$CURRENT_BACKUP_DIR"

echo "创建当前文件备份到: $CURRENT_BACKUP_DIR"
cp "$PREMIUM_FILE" "$CURRENT_BACKUP_DIR/PremiumEndpointAspect.java.backup"
cp "$ENTERPRISE_FILE" "$CURRENT_BACKUP_DIR/EnterpriseEndpointAspect.java.backup"
cp "$LICENSE_FILE" "$CURRENT_BACKUP_DIR/LicenseKeyChecker.java.backup"

echo ""
echo "应用修改..."

# 应用PremiumEndpointAspect修改
echo "修改 PremiumEndpointAspect.java..."
cat > "$PREMIUM_FILE" << 'EOF'
package stirling.software.proprietary.security.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

@Aspect
@Component
public class PremiumEndpointAspect {

    private final boolean runningProOrHigher;

    public PremiumEndpointAspect(@Qualifier("runningProOrHigher") boolean runningProOrHigher) {
        this.runningProOrHigher = runningProOrHigher;
    }

    @Around(
            "@annotation(stirling.software.proprietary.security.config.PremiumEndpoint) || @within(stirling.software.proprietary.security.config.PremiumEndpoint)")
    public Object checkPremiumAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        // 权限检查已禁用 - 允许所有Premium功能访问
        // if (!runningProOrHigher) {
        //     throw new ResponseStatusException(
        //             HttpStatus.FORBIDDEN, "This endpoint requires a Pro or higher license");
        // }
        return joinPoint.proceed();
    }
}
EOF

# 应用EnterpriseEndpointAspect修改
echo "修改 EnterpriseEndpointAspect.java..."
cat > "$ENTERPRISE_FILE" << 'EOF'
package stirling.software.proprietary.security.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

@Aspect
@Component
public class EnterpriseEndpointAspect {

    private final boolean runningEE;

    public EnterpriseEndpointAspect(@Qualifier("runningEE") boolean runningEE) {
        this.runningEE = runningEE;
    }

    @Around(
            "@annotation(stirling.software.proprietary.security.config.EnterpriseEndpoint) || @within(stirling.software.proprietary.security.config.EnterpriseEndpoint)")
    public Object checkEnterpriseAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        // 权限检查已禁用 - 允许所有Enterprise功能访问
        // if (!runningEE) {
        //     throw new ResponseStatusException(
        //             HttpStatus.FORBIDDEN, "This endpoint requires an Enterprise license");
        // }
        return joinPoint.proceed();
    }
}
EOF

echo "修改 LicenseKeyChecker.java..."
# 注意：这里需要保留原文件的大部分内容，只修改特定方法
# 由于文件较大，我们使用sed进行精确替换

# 备份原文件
cp "$LICENSE_FILE" "$LICENSE_FILE.tmp"

# 应用修改
sed -i.bak '
# 修改构造函数
/public LicenseKeyChecker(/,/}/ {
    s/this\.checkLicense();/\/\/ 初始化验证已禁用 - 直接设置为Enterprise许可证\n        this.premiumEnabledResult = License.ENTERPRISE;\n        log.info("License check bypassed during initialization - set to Enterprise");\n        \/\/ this.checkLicense();/
}

# 修改定期验证方法
s/@Scheduled(initialDelay = 604800000, fixedRate = 604800000)/\/\/ 定期验证已禁用 - 不再发送网络验证请求\n    \/\/ @Scheduled(initialDelay = 604800000, fixedRate = 604800000)/

# 修改定期验证方法体
/public void checkLicensePeriodically()/ {
    N
    s/checkLicense();/\/\/ checkLicense();\n        log.info("License periodic check disabled - skipping verification");/
}

# 修改更新许可证方法
/public void updateLicenseKey/,/}/ {
    s/checkLicense();/\/\/ 许可证更新验证已禁用 - 保持Enterprise状态\n        log.info("License key updated but verification bypassed - maintaining Enterprise status");\n        \/\/ checkLicense();/
}
' "$LICENSE_FILE"

echo ""
echo "=== 修改完成 ==="
echo "许可证验证绕过修改已应用"
echo ""
echo "当前文件已备份到: $CURRENT_BACKUP_DIR"
echo ""
echo "接下来的步骤:"
echo "1. 重新编译项目: ./gradlew clean build"
echo "2. 重启应用: ./gradlew bootRun"
echo ""
echo "注意: 修改后所有Pro/Enterprise功能都将可用，且不会发送网络验证请求"
EOF
