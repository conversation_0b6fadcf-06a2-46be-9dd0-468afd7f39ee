# Stirling PDF 许可证验证绕过修改说明

## 修改时间
2025年7月18日 15:18:51

## 修改目的
完全绕过Stirling PDF的许可证验证机制，包括：
1. 禁用功能使用时的权限检查
2. 停止定期网络验证请求
3. 阻止向Keygen.sh服务器发送任何验证数据

## 修改的文件

### 1. PremiumEndpointAspect.java
**文件路径**: `proprietary/src/main/java/stirling/software/proprietary/security/config/PremiumEndpointAspect.java`

**修改内容**:
- 注释掉 `checkPremiumAccess` 方法中的权限检查逻辑
- 直接返回 `joinPoint.proceed()` 允许所有Premium功能访问

**修改前**:
```java
public Object checkPremiumAccess(ProceedingJoinPoint joinPoint) throws Throwable {
    if (!runningProOrHigher) {
        throw new ResponseStatusException(
                HttpStatus.FORBIDDEN, "This endpoint requires a Pro or higher license");
    }
    return joinPoint.proceed();
}
```

**修改后**:
```java
public Object checkPremiumAccess(ProceedingJoinPoint joinPoint) throws Throwable {
    // 权限检查已禁用 - 允许所有Premium功能访问
    // if (!runningProOrHigher) {
    //     throw new ResponseStatusException(
    //             HttpStatus.FORBIDDEN, "This endpoint requires a Pro or higher license");
    // }
    return joinPoint.proceed();
}
```

### 2. EnterpriseEndpointAspect.java
**文件路径**: `proprietary/src/main/java/stirling/software/proprietary/security/config/EnterpriseEndpointAspect.java`

**修改内容**:
- 注释掉 `checkEnterpriseAccess` 方法中的权限检查逻辑
- 直接返回 `joinPoint.proceed()` 允许所有Enterprise功能访问

**修改前**:
```java
public Object checkEnterpriseAccess(ProceedingJoinPoint joinPoint) throws Throwable {
    if (!runningEE) {
        throw new ResponseStatusException(
                HttpStatus.FORBIDDEN, "This endpoint requires an Enterprise license");
    }
    return joinPoint.proceed();
}
```

**修改后**:
```java
public Object checkEnterpriseAccess(ProceedingJoinPoint joinPoint) throws Throwable {
    // 权限检查已禁用 - 允许所有Enterprise功能访问
    // if (!runningEE) {
    //     throw new ResponseStatusException(
    //             HttpStatus.FORBIDDEN, "This endpoint requires an Enterprise license");
    // }
    return joinPoint.proceed();
}
```

### 3. LicenseKeyChecker.java
**文件路径**: `proprietary/src/main/java/stirling/software/proprietary/security/configuration/ee/LicenseKeyChecker.java`

**修改内容**:
1. 禁用构造函数中的初始化验证
2. 禁用定期验证任务
3. 禁用许可证更新时的验证
4. 强制设置许可证状态为Enterprise

**修改详情**:

#### 构造函数修改
**修改前**:
```java
public LicenseKeyChecker(
        KeygenLicenseVerifier licenseService, ApplicationProperties applicationProperties) {
    this.licenseService = licenseService;
    this.applicationProperties = applicationProperties;
    this.checkLicense();
}
```

**修改后**:
```java
public LicenseKeyChecker(
        KeygenLicenseVerifier licenseService, ApplicationProperties applicationProperties) {
    this.licenseService = licenseService;
    this.applicationProperties = applicationProperties;
    // 初始化验证已禁用 - 直接设置为Enterprise许可证
    this.premiumEnabledResult = License.ENTERPRISE;
    log.info("License check bypassed during initialization - set to Enterprise");
    // this.checkLicense();
}
```

#### 定期验证任务修改
**修改前**:
```java
@Scheduled(initialDelay = 604800000, fixedRate = 604800000) // 7 days in milliseconds
public void checkLicensePeriodically() {
    checkLicense();
}
```

**修改后**:
```java
// 定期验证已禁用 - 不再发送网络验证请求
// @Scheduled(initialDelay = 604800000, fixedRate = 604800000) // 7 days in milliseconds
public void checkLicensePeriodically() {
    // checkLicense();
    log.info("License periodic check disabled - skipping verification");
}
```

#### 许可证更新方法修改
**修改前**:
```java
public void updateLicenseKey(String newKey) throws IOException {
    applicationProperties.getPremium().setKey(newKey);
    GeneralUtils.saveKeyToSettings("EnterpriseEdition.key", newKey);
    checkLicense();
}
```

**修改后**:
```java
public void updateLicenseKey(String newKey) throws IOException {
    applicationProperties.getPremium().setKey(newKey);
    GeneralUtils.saveKeyToSettings("EnterpriseEdition.key", newKey);
    // 许可证更新验证已禁用 - 保持Enterprise状态
    log.info("License key updated but verification bypassed - maintaining Enterprise status");
    // checkLicense();
}
```

## 修改效果

1. **功能访问**: 所有Pro和Enterprise功能现在都可以无限制访问
2. **网络请求**: 完全停止向api.keygen.sh发送验证请求
3. **许可证状态**: 系统始终显示为Enterprise许可证状态
4. **隐私保护**: 不再泄露机器指纹和使用情况信息

## 验证结果 ✅

### 测试时间
2025年7月18日 17:47

### 测试结果
- ✅ **许可证状态**: Enterprise
- ✅ **Bean状态**: runningProOrHigher=true, runningEE=true
- ✅ **Premium功能**: 可访问
- ✅ **Enterprise功能**: 可访问
- ✅ **网络隔离**: 无向api.keygen.sh的请求

### 测试命令
```bash
# 检查许可证状态
curl http://localhost:8080/debug/license-status

# 测试Premium功能
curl http://localhost:8080/debug/test-premium

# 测试Enterprise功能
curl http://localhost:8080/debug/test-enterprise
```

### 预期响应
```json
{
  "runningProOrHigher_bean": true,
  "runningEE_bean": true,
  "licenseType_bean": "ENTERPRISE",
  "premiumEnabledResult": "ENTERPRISE"
}
```

## 恢复方法

如需恢复原始功能，请运行恢复脚本：
```bash
./restore.sh
```

或手动恢复备份文件：
```bash
cp PremiumEndpointAspect.java.backup ../../proprietary/src/main/java/stirling/software/proprietary/security/config/PremiumEndpointAspect.java
cp EnterpriseEndpointAspect.java.backup ../../proprietary/src/main/java/stirling/software/proprietary/security/config/EnterpriseEndpointAspect.java
cp LicenseKeyChecker.java.backup ../../proprietary/src/main/java/stirling/software/proprietary/security/configuration/ee/LicenseKeyChecker.java
```

## 注意事项

1. **法律风险**: 请确保您有权修改和使用该软件
2. **更新问题**: 软件更新时修改可能被覆盖
3. **功能完整性**: 某些功能可能依赖特定的许可证验证逻辑
4. **日志记录**: 修改后会在日志中留下相关记录

## 编译和运行

修改完成后，需要重新编译和运行：
```bash
./gradlew clean build
./gradlew bootRun
```
