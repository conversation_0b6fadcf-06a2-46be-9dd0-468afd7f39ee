#!/bin/bash

# Stirling PDF 许可证验证恢复脚本
# 创建时间: $(date)
# 用途: 恢复原始的许可证验证功能

echo "=== Stirling PDF 许可证验证恢复脚本 ==="
echo "此脚本将恢复原始的许可证验证功能"
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "备份目录: $SCRIPT_DIR"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 检查备份文件是否存在
if [ ! -f "$SCRIPT_DIR/PremiumEndpointAspect.java.backup" ]; then
    echo "错误: 找不到 PremiumEndpointAspect.java.backup"
    exit 1
fi

if [ ! -f "$SCRIPT_DIR/EnterpriseEndpointAspect.java.backup" ]; then
    echo "错误: 找不到 EnterpriseEndpointAspect.java.backup"
    exit 1
fi

if [ ! -f "$SCRIPT_DIR/LicenseKeyChecker.java.backup" ]; then
    echo "错误: 找不到 LicenseKeyChecker.java.backup"
    exit 1
fi

echo "所有备份文件检查完成"
echo ""

# 询问用户确认
read -p "确定要恢复原始的许可证验证功能吗? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始恢复文件..."

# 恢复文件
echo "恢复 PremiumEndpointAspect.java..."
cp "$SCRIPT_DIR/PremiumEndpointAspect.java.backup" \
   "$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/config/PremiumEndpointAspect.java"

echo "恢复 EnterpriseEndpointAspect.java..."
cp "$SCRIPT_DIR/EnterpriseEndpointAspect.java.backup" \
   "$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/config/EnterpriseEndpointAspect.java"

echo "恢复 LicenseKeyChecker.java..."
cp "$SCRIPT_DIR/LicenseKeyChecker.java.backup" \
   "$PROJECT_ROOT/proprietary/src/main/java/stirling/software/proprietary/security/configuration/ee/LicenseKeyChecker.java"

echo ""
echo "=== 恢复完成 ==="
echo "原始的许可证验证功能已恢复"
echo ""
echo "接下来的步骤:"
echo "1. 重新编译项目: ./gradlew clean build"
echo "2. 重启应用: ./gradlew bootRun"
echo ""
echo "注意: 恢复后将重新启用许可证验证，需要有效的许可证才能使用Pro/Enterprise功能"
