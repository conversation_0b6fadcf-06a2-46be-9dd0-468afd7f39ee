# Stirling PDF 许可证验证绕过

## 概述

这个备份目录包含了Stirling PDF许可证验证绕过的相关文件和脚本。修改的目的是完全绕过Stirling PDF的许可证验证机制，使所有Pro和Enterprise功能都可以无限制使用，同时停止向Keygen.sh服务器发送任何验证请求。

## 目录内容

- **原始文件备份**
  - `PremiumEndpointAspect.java.backup` - Pro功能权限检查原始文件
  - `EnterpriseEndpointAspect.java.backup` - Enterprise功能权限检查原始文件
  - `LicenseKeyChecker.java.backup` - 许可证验证器原始文件

- **脚本**
  - `restore.sh` - 恢复原始许可证验证功能的脚本
  - `apply_modifications.sh` - 重新应用许可证验证绕过修改的脚本

- **文档**
  - `README.md` - 本文档
  - `MODIFICATION_SUMMARY.md` - 详细的修改说明文档

## 修改内容概述

1. **禁用功能权限检查**
   - 修改AOP切面，允许所有Pro和Enterprise功能访问

2. **停止网络验证请求**
   - 禁用定期验证任务
   - 禁用初始化验证
   - 禁用许可证更新时的验证

3. **强制设置许可证状态**
   - 将许可证状态强制设置为Enterprise

## 使用方法

### 恢复原始功能

如果需要恢复原始的许可证验证功能，请运行：

```bash
./restore.sh
```

### 重新应用修改

如果软件更新后需要重新应用许可证验证绕过修改，请运行：

```bash
./apply_modifications.sh
```

## 注意事项

1. **法律风险**：请确保您有权修改和使用该软件
2. **更新问题**：软件更新时修改可能被覆盖，需要重新应用
3. **功能完整性**：某些功能可能依赖特定的许可证验证逻辑
4. **日志记录**：修改后会在日志中留下相关记录

## 修改后的编译和运行

修改完成后，需要重新编译和运行：

```bash
./gradlew clean build
./gradlew bootRun
```

## 详细信息

有关修改的详细信息，请查看 `MODIFICATION_SUMMARY.md` 文件。
