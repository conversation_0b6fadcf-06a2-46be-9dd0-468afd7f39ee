// Configure bootRun to disable it or point to a main class
bootRun {
    enabled = false
}
spotless {
    java {
        target sourceSets.main.allJava
        googleJavaFormat(googleJavaFormatVersion).aosp().reorderImports(false)

        importOrder("java", "javax", "org", "com", "net", "io", "jakarta", "lombok", "me", "stirling")
        toggleOffOn()
        trimTrailingWhitespace()
        leadingTabsToSpaces()
        endWithNewline()
    }
}
dependencies {
    api 'org.springframework.boot:spring-boot-starter-web'
    api 'org.springframework.boot:spring-boot-starter-aop'
    api 'org.springframework.boot:spring-boot-starter-thymeleaf'
    api 'com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:20240325.1'
    api 'com.fathzer:javaluator:3.0.6'
    api 'com.posthog.java:posthog:1.2.0'
    api 'org.apache.commons:commons-lang3:3.17.0'
    api 'com.drewnoakes:metadata-extractor:2.19.0' // Image metadata extractor
    api 'com.vladsch.flexmark:flexmark-html2md-converter:0.64.8'
    api "org.apache.pdfbox:pdfbox:$pdfboxVersion"
    api 'jakarta.servlet:jakarta.servlet-api:6.1.0'
    api 'org.snakeyaml:snakeyaml-engine:2.9'
    api "org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9"
    api 'jakarta.mail:jakarta.mail-api:2.1.3'
    runtimeOnly 'org.eclipse.angus:angus-mail:2.0.3'
}
