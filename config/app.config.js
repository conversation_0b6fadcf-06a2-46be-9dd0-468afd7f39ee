// 小程序配置文件
// 在这里修改后端接口地址

const config = {
  // 后端API配置
  api: {
    // 后端服务器地址 - 修改这里的地址指向您的Stirling-PDF后端
    baseURL: 'http://192.168.1.128',
    
    // API版本
    version: 'v1',
    
    // 请求超时时间 (毫秒)
    timeout: 60000,
    
    // API Key (如果后端启用了认证)
    apiKey: ''
  },
  
  // 文件上传配置
  upload: {
    // 最大文件大小 (字节) - 50MB
    maxFileSize: 500 * 1024 * 1024,

    // 支持的文件类型
    allowedTypes: [
      'application/pdf',
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
      'application/msword', // doc
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'application/vnd.ms-excel', // xls
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
      'application/vnd.ms-powerpoint', // ppt
      'text/html',
      'text/plain'
    ],

    // 文件扩展名到MIME类型的映射
    extensions: {
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'webp': 'image/webp',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'xls': 'application/vnd.ms-excel',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'ppt': 'application/vnd.ms-powerpoint',
      'html': 'text/html',
      'htm': 'text/html',
      'txt': 'text/plain'
    }
  }
};

// 获取API完整URL
function getApiUrl(endpoint) {
  return `${config.api.baseURL}/api/${config.api.version}${endpoint}`;
}

// 获取配置项
function get(path) {
  return path.split('.').reduce((obj, key) => obj && obj[key], config);
}

module.exports = {
  config,
  getApiUrl,
  get
};
